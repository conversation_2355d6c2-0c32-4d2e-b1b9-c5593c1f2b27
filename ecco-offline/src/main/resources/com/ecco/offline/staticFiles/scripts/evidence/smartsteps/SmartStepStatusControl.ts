import $ = require("jquery");
import _ = require("lodash");
import environment = require("../../environment");
import ClickableImageControl = require("../../controls/ClickableImageControl");
import Element = require("../../controls/Element");

import * as dto from "ecco-dto/evidence-dto";
import {SmartStepStatusTransitions} from "ecco-evidence";
import { SmartStepStatus, SmartStepDisplaySymbol } from 'ecco-dto';

/**
 * NewRelic JS monitoring if enabled
 * See https://docs.newrelic.com/docs/browser/new-relic-browser/browser-agent-apis/noticing-or-logging-front-end-errors
 */
declare class _NREUM { noticeError(err: Error): void; }
declare var NREUM: _NREUM;

/**
 * Visual representation of a status control
 */
class SmartStepStatusControl implements Element {

    private statusControl: ClickableImageControl;

    private currentStatus: dto.SmartStepStatus;


     private static symbolImages: { symbol: SmartStepDisplaySymbol; imgClass: string }[] = [
            { symbol: SmartStepDisplaySymbol.FullPlus, imgClass: "plus24" },
            { symbol: SmartStepDisplaySymbol.FadedPlus, imgClass: "plus-faded24" },
            { symbol: SmartStepDisplaySymbol.FullStar, imgClass: "star24" },
            { symbol: SmartStepDisplaySymbol.FadedStar, imgClass: "star-faded24" },
            { symbol: SmartStepDisplaySymbol.CommentOnly, imgClass: "commentOnly" },
        ];

    constructor(private initialStatus: SmartStepStatus, private statusTransitions: SmartStepStatusTransitions,
        private onClick: (newStatus: SmartStepStatus) => void) {

        this.currentStatus = initialStatus;

        const symbol: SmartStepDisplaySymbol = this.statusTransitions.getDisplaySymbol(this.initialStatus, this.currentStatus);
        const symbolSrc = SmartStepStatusControl.symbolSrc(symbol);
        this.statusControl = new ClickableImageControl(symbolSrc, () => {
            this.currentStatus = this.statusTransitions.getNextStatus(this.initialStatus, this.currentStatus);
            this.onClick(this.currentStatus);
            this.setStatus(this.currentStatus);
        });
    }


    public element(): $.JQuery {
        return this.statusControl.element();
    }

    public setStatus(status: SmartStepStatus) {
        const symbol = this.statusTransitions.getDisplaySymbol(this.initialStatus, status);
        this.statusControl.setSrc( SmartStepStatusControl.symbolSrc(symbol) );
    }

    public getStatus() {
        return this.currentStatus;
    }

    /** get the icon to use for the symbol provided */
    public static symbolSrc(symbol: SmartStepDisplaySymbol): string {
        let imgClass;
        for (let key in SmartStepStatusControl.symbolImages) {
            if (SmartStepStatusControl.symbolImages[key].symbol == symbol) {
                imgClass = SmartStepStatusControl.symbolImages[key].imgClass;
                // get the escaped path of the icon
                return imgClass;
            }
        }

        if (NREUM) {
            const err = new Error("No icon exists for status: " + symbol);
            NREUM.noticeError(err);
        }
        return null;
    }
}
export = SmartStepStatusControl;