import $ = require("jquery");
import SupportInstanceControl = require("../../evidence/multi-instance/SupportInstanceControl");
import OutcomesControl = require("./OutcomesControl");
import OutcomeControl = require("./OutcomeControl");
import ActionButton = require("../../controls/ActionButton");
import AddGoalForm = require("../../evidence/multi-instance/AddGoalForm");
import ModalMode = require("../../controls/ModalMode");
import Modal = require("../../controls/Modal");
import evidenceEvents = require("../../evidence/events");
import services = require("ecco-offline-data");
import Lazy = require("lazy");
import Sequence = LazyJS.Sequence;
import {
    ActionInstanceControlDataSetup,
    ActionInstanceFeatures,
    EvidenceDef
} from "ecco-evidence";
import * as evidenceDto from "ecco-dto/evidence-dto";
import {Action, EvidenceUpdateEvent, ServiceRecipientWithEntities, SmartStepStatus} from "ecco-dto";
import {Command, CommandQueue, GoalUpdateCommand} from "ecco-commands";
import {GoalUpdateCommandDto, SupportAction} from "ecco-dto/evidence-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {ActivityType} from "ecco-dto/service-config-dto";
import {ActionInstanceControl} from "../evidenceControls";
import {GoalUpdateEvent} from "../events";
import {EccoDateTime, ReloadEvent, ResizeEvent} from "@eccosolutions/ecco-common";
import {getCommandQueueRepository, showErrorAsAlert} from "ecco-offline-data";

function checkIfInView(element: $.JQuery): boolean {
    let offset = element.offset().top - $(window).scrollTop();

    if(offset > window.innerHeight){
        // Not in view so scroll to it
        $('html,body').animate({scrollTop: offset}, 1000);
        return false;
    }
    return true;
}

/**
 * A basic control which is initially blank, but shows each supportAction
 * on a separate line - without any group divides, sub-steps etc.
 */
class GoalsControl extends OutcomesControl {

    private controls: Array<ActionInstanceControl> = [];
    private $containerUl: $.JQuery;
    private addGoalImage: string;
    private controlFeatures: ActionInstanceFeatures;
    private addGoalModal: Modal;
    private formQueue: CommandQueue;

    public static forwardPlanControl(serviceRecipientId: number, type: "support" | "risk" | "care"): Promise<GoalsControl> {
        const asForwardRiskPlan = type == "risk";
        const asForwardCarePlan = type == "care";
        const asForwardSupportPlan = type == "support";
        return services.getReferralRepository().findOneServiceRecipientWithEntities(serviceRecipientId).then(referral => {
            // super calls .load()
            return new GoalsControl(undefined, referral, [], () => null,
                EvidenceDef.fromTaskName(referral.features, referral.configResolver.getServiceType(),
                        asForwardRiskPlan
                            ? referral.configResolver.getServiceType().getFirstRiskTaskName(referral.features)
                            : referral.configResolver.getServiceType().getFirstSupportTaskName(referral.features)
                ),
                () => {}, asForwardCarePlan, asForwardSupportPlan, asForwardRiskPlan);
                //ReloadEvent.bus.addHandler(() => form.emptyAndLoad());
        });
    }

    public static loadAndAttachForwardPlan($containerWrapper: $.JQuery, planAllocatedId: string, serviceRecipientId: number,
                                           asForwardCarePlan: boolean, asForwardSupportPlan: boolean, asForwardRiskPlan: boolean) {
        services.getReferralRepository().findOneServiceRecipientWithEntities(serviceRecipientId).then(referral => {
            // super calls .load()
            let form = new GoalsControl(planAllocatedId, referral, [], () => null,
                    EvidenceDef.fromTaskName(referral.features, referral.configResolver.getServiceType(),
                            asForwardRiskPlan
                                    ? referral.configResolver.getServiceType().getFirstRiskTaskName(referral.features)
                                    : referral.configResolver.getServiceType().getFirstSupportTaskName(referral.features)
                    ),
                () => {}, asForwardCarePlan, asForwardSupportPlan, asForwardRiskPlan);
            $containerWrapper.append(form.element());
            ReloadEvent.bus.addHandler(() => form.emptyAndLoad());
        });
    }

    /**
     * construct the control to display smart steps - which requires information on what to display and save
     */
    public constructor(planAllocatedId: string, serviceRecipient: ServiceRecipientWithEntities,
            serviceRecipientActivityTypeInterest: ActivityType[],
            getWorkUuid: () => Uuid,
            evidenceDef: EvidenceDef,
            flagEvidenceCallback?: (flagEvidenceByFlagId: {[id: number]: evidenceDto.FlagEvidenceDto}) => void,
            asForwardCarePlan = false, asForwardPlan = false, asForwardRiskPlan = false) {
        super(planAllocatedId, serviceRecipient, serviceRecipientActivityTypeInterest, getWorkUuid, evidenceDef, flagEvidenceCallback, asForwardCarePlan, asForwardPlan, asForwardRiskPlan);
    }

    public domElement(): HTMLElement {
        return this.getContainer()[0];
    }

    public emitChangesTo(queue: CommandQueue) {
        queue.addQueue(this.formQueue);
        this.controls.forEach( (control) => control.emitChangesTo(queue) );
    }

    public renderWithPage(idx: number): $.JQuery {
        throw Error("wizard usage not designed for this control");
    }

    protected initialise() {
        super.initialise();
        this.controlFeatures = new ActionInstanceFeatures(this.context,  0);
        this.addGoalImage = "plus24";
        this.addGoalModal = new Modal("modal-full", ModalMode.fillScreenHeight);
        this.formQueue = new CommandQueue(getCommandQueueRepository());
    }

    protected populateContainer() {
        if (this.controlFeatures.isAllowFromBlankUi()) {
            this.$containerUl = OutcomeControl.$template.clone();
            this.$container.append(this.$containerUl);
        }

        let addGoal = new ActionButton(null) // NB BaseEvidenceForm uses 'options' for an alternative name
            .addClass("btn btn-default")
            .autoDisable(false)
            .clickSynchronous(() => this.createNewAction());
        addGoal.element().append($("<span>")
                                     .attr("role", "img")
                                     .addClass("status-image")
                                     .addClass(this.addGoalImage));

        if (this.context.asForwardPlan || this.context.asForwardRiskPlan) {
            addGoal.element().hide();
        }

        this.$container.prepend($("<div>").append(addGoal.element()));
    }

    private createNewAction() {
        if (this.controlFeatures.isAllowFromBlankUi()) {
            this.createNewActionInSitu();
        } else {
            this.createNewActionForm();
        }
    }

    protected clearAllControls() {
        super.clearAllControls();
        this.controls = [];
    }

    /** prime the controls with definition and instance data */
    protected createAllControls(): Promise<any>[] {

        let actionsToProcess: Sequence<SupportAction> = Lazy(this.latestActionsToProcess);

        if (this.context.asForwardPlan || this.context.asForwardRiskPlan) {
            const outcomeIds = this.serviceRecipient.configResolver.getServiceType().getOutcomes().map(o => o.getId());
            const riskAreasIds = this.serviceRecipient.configResolver.getServiceType().getRiskAreas().map(o => o.getId());
            actionsToProcess = actionsToProcess
                // check the action is enabled
                .filter(a => !this.serviceRecipient.features.isAnyActionByIdDisabled(a.actionId))
                // check the outcome is configured on the service type
                .filter(a => {
                    const oId = this.serviceRecipient.features.getAnyActionById(a.actionId).getOutcome().getId();
                    return outcomeIds.some(id => id == oId) || riskAreasIds.some(id => id == oId);
                })
                .filter(sa => sa.targetDateTime != null)
                .filter(sa => sa.status != SmartStepStatus.Achieved)
                .filter(sa => sa.status != SmartStepStatus.NoLongerWanted)
                .filter(sa => sa.status != SmartStepStatus.CommentOnly)
                .sort((sa1, sa2) => EccoDateTime.parseIso8601(sa1.targetDateTime).compare(EccoDateTime.parseIso8601(sa2.targetDateTime)));
        }

        actionsToProcess.each(supportAction => {
            let actionDef =  this.serviceRecipient.configResolver.getServiceType().getAnyActionById(supportAction.actionId);
            if (actionDef) {
                let initialData = ActionInstanceControlDataSetup.fromSnapshot(supportAction, false, this.context.serviceRecipientActivityInterest);

                const actionFeatures = new ActionInstanceFeatures(this.context, supportAction.hierarchy);
                actionFeatures.setAllowFromBlankUi(initialData.asCreatingFromEmpty);

                // NB may be a riskInstanceControl, but we only need the same display info as support, so just leave for now
                let supportInstanceControl = new SupportInstanceControl(this.context, this.serviceRecipient.features,
                    this.serviceRecipient.serviceRecipientId, this.evidenceDef,
                    actionDef, initialData, initialData.actionInstanceUuid, actionFeatures);
                supportInstanceControl.init();
                this.controls.push(supportInstanceControl);
            }
        });

        return [];
    }

    protected renderAll(): void {
        this.controls.forEach( control => {
            control.render();
            let $elm = control.element();
            this.getContainer().append($elm);
            return $elm;
        });

        if (!this.controls || this.controls.length == 0) {
            const $msg = $("<span>").text("no target dates to show on the forward plan")
            this.getContainer().append($("<div>").addClass("text-center").append($msg))
        }

    }

    private getContainer() {
        if (this.controlFeatures.isAllowFromBlankUi()) {
            return this.$containerUl;
        } else {
            return this.$container;
        }
    }

    /** True if required fields are set */
    public isValid(): boolean {
        // return false early through every
        return this.controls.every((ctl) => {
            return ctl.isValid();
        });
    }

    private createNewActionForm() {
        // this is from ChecksEvidenceForm which could be used in place of this class
        // but MIEF is an independent form, so does not expect to have a comment area etc

        let form = new AddGoalForm(this.serviceRecipient, this.evidenceDef);
        form.onSubmit( (commandQueue: CommandQueue) => {
            this.applyCommands(commandQueue)
                .then(() => this.addGoalModal.dialogHide())
                .catch(showErrorAsAlert);
        });
        this.addGoalModal.title("add goal");
        this.addGoalModal.setFooter(form.getFooter());
        this.addGoalModal.popWithJQueryContent(form.element());
    }

    /** Allows the current content of a command queue to be applied - only really works where that command
     * queue is NOT being sent - as otherwise we expect the command queue to be flushed and therefore be empty.
     * In those scenarios updated will happen via events as added by addCommandQueueHandlers() */
    protected applyCommands(commandQueue: CommandQueue): Promise<void> {
        this.formQueue.addQueue(commandQueue);
        return commandQueue.getCommands()
            .then(commands => {this.applyCommandArray(commands);});
    }
    protected applyCommandArray(cmds: Command[]) {
        cmds.forEach(cmd => {
            GoalsControl.applyCommand(cmd, cmd.toCommandDto());
        });
    }
    private static applyCommand(command: Command, dto: any) {
        console.info("GoalsControl cmd: %s", JSON.stringify(dto));
        // TODO check instanceof due to 'adapt' in CommandQueue.addCommand
        if (command instanceof GoalUpdateCommand) {
            evidenceEvents.GoalUpdateEvent.bus.fire(new GoalUpdateEvent(<GoalUpdateCommand>command, dto));
            // Things like MultiInstanceListControl listen here
            EvidenceUpdateEvent.bus(command.getEvidenceGroupName())
                .fire(new EvidenceUpdateEvent(dto));
        }
    }

    private createNewActionInSitu() {
        let initialData = ActionInstanceControlDataSetup.fromEmpty(true, this.context.serviceRecipientActivityInterest);
        initialData.status = SmartStepStatus.WantToAchieve;

        let supportInstanceControl = new SupportInstanceControl(this.context, this.serviceRecipient.features,
            this.serviceRecipient.serviceRecipientId, this.evidenceDef,
            null, initialData, Uuid.randomV4(), this.controlFeatures);
        supportInstanceControl.init();

        this.controls.unshift(supportInstanceControl);
        supportInstanceControl.render();
        let $elm = supportInstanceControl.element();
        this.getContainer().prepend($elm);
        ResizeEvent.bus.fire(new ResizeEvent());
        // Ensure the specific smart step is also visible
        checkIfInView($elm);
    }

    /** Mark an action as appropriate and set its specific goal/comment
     * This is called when MultiActionsControl.createNew fires the event.
     */
    // see BaseEvidenceForm.applyCommand which says graphical approach uses addGoal - but AddGoalForm should too
    // @Override
    public updateAction(goalUpdateCommand: GoalUpdateCommand): void {
        let dto = <GoalUpdateCommandDto> goalUpdateCommand.toCommandDto();
        let actionDefId = dto.actionDefId;
        let actionDef: Action = this.serviceRecipient.configResolver.getServiceType().getActionById(actionDefId);

        if (actionDef) {
            let initialData = ActionInstanceControlDataSetup.fromEmpty(false, this.context.serviceRecipientActivityInterest);
            initialData.status = SmartStepStatus.WantToAchieve;
            initialData.goalName = dto.goalNameChange && dto.goalNameChange.to;
            initialData.goalPlan = dto.goalPlanChange && dto.goalPlanChange.to;

            let supportInstanceControl = new SupportInstanceControl(this.context, this.serviceRecipient.features,
                this.serviceRecipient.serviceRecipientId, this.evidenceDef,
                actionDef, initialData, initialData.actionInstanceUuid, this.controlFeatures);
            supportInstanceControl.init();

            this.controls.push(supportInstanceControl);
            supportInstanceControl.render();
            let $elm = supportInstanceControl.element();
            this.getContainer().append($elm);
            ResizeEvent.bus.fire(new ResizeEvent());
            // Ensure the specific smart step is also visible
            checkIfInView($elm);
        }
    }

    public updateQuestionAnswer(questionDefId: number, answer: string): void {
    }
}
export = GoalsControl;
