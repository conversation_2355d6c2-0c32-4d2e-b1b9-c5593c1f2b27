import $ = require("jquery");
import _ = require("lodash");
import * as commandDtos from "ecco-dto/command-dto";
import * as dto from "ecco-dto/evidence-dto";
import DL = require("../../controls/DefinitionList");

import BaseControl = require("../../controls/BaseControl");
import {StringUtils} from "@eccosolutions/ecco-common";
import {EccoDateTime} from "@eccosolutions/ecco-common";

var MAX_TITLE_LEN = 50;

var statusMessages = {
    newNeed: { title: "new need", iconPath: "plus24" },
    removedNeed: { title: "not relevant", iconPath: "plus-faded24" },
    fulfilledNeed: { title: "achieved", iconPath: "star24" },
    revertedFulfilledNeed: { title: "not achieved", iconPath: "star-faded24" },
    // commentOnly not on MyPlanHistory
};

class MyPlanHistoryControl extends BaseControl {

    private commandDto: dto.GoalUpdateCommandDto;
    private latestTitle: string;
    private isNew: boolean;

    constructor(commandDto: dto.GoalUpdateCommandDto, latestTitle: string, isNew: boolean) {
        var $container = $("<li>");
        super($container);
        this.commandDto = commandDto;
        this.latestTitle = latestTitle;
        this.isNew = isNew;
        this.render();
    }


    private render() {
        var timestamp = EccoDateTime.parseIso8601Utc(this.commandDto.timestamp);
        var title = StringUtils.trimText(this.latestTitle, MAX_TITLE_LEN);

        var $title = $("<span>").text(title || "(no need recorded)");
        var $header = $("<div>").addClass("comment");
        if (this.isNew) {
            $header.append( $("<span>")
                .attr("role", "img")
                .addClass("status-image")
                .addClass(statusMessages.newNeed.iconPath)
                .attr("title", statusMessages.newNeed.title) );
        }
        $header.append($title);

        this.element().empty()
            .append( _.escape(timestamp.formatLocalShort()));

        this.element()
            .append($("<span>").addClass('user')
                .append(" recorded by ")
                .append( $("<em>").text(this.commandDto.userName) ) )
            .append($header);

        var $list = $("<ul>").addClass("list-unstyled changes container-fluid row");
        this.addChangeItem( $list, "need", this.commandDto.goalNameChange );
        this.addChangeItem( $list, "objectives", this.commandDto.annotationChange["objectives"]);
        this.addChangeItem( $list, "actions", this.commandDto.annotationChange["actions"]);
        this.addChangeItem( $list, "comments", this.commandDto.annotationChange["comments"]);
        this.element().append($list);
    }

    private addChangeItem($list: $.JQuery, field: string, change: commandDtos.StringChangeOptional) {
        if (change) {
            $list.append(
                $("<div>").addClass("col-sm-3")
                    .append( $("<div>").addClass("updated").text( field ) )
                    .append(
                        new DL()
                            .addEntry("now:", change.to || "(deleted)")
                            .addEntry("previously:", change.from || "(empty)")
                            .element()
                    )
            );
        }
        else {
            $list.append(
                $("<div>").addClass("col-sm-3")
                    .append( $("<div>").addClass("unchanged").text( field ) )
                    .append( $("<div>").addClass("small").text("(no change)" ) ) );
        }
    }
}
export = MyPlanHistoryControl;
