import $ = require("jquery");
import _ = require("lodash");
import ClickableImageControl = require("../../controls/ClickableImageControl");
import events = require("../events");
import InputGroup = require("../../controls/InputGroup");
import SmartStepStatusControl = require("../smartsteps/SmartStepStatusControl");

import TextInput = require("../../controls/TextInput");
import TextAreaInput = require("../../controls/TextAreaInput");
import SelectList = require("../../controls/SelectList");
import BaseActionInstanceControl = require("./BaseActionInstanceControl");
import {ActivityInterestChangeCommand, CommandQueue, GoalUpdateCommand} from "ecco-commands";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {ActivityType} from "ecco-dto/service-config-dto";
import {
    ActionComponent,
    asNumberChange,
    diff,
    EvidencePageType,
    Outcome,
    ServiceTypeElement,
    SessionData,
    SmartStepStatus,
    TaskNames
} from "ecco-dto";
import {ActionInstanceControl} from "../evidenceControls";
import {
    ActionInstanceControlData,
    ActionInstanceFeatures,
    ActionInstanceState,
    EvidenceContext,
    EvidenceDef
} from "ecco-evidence";
import {CronScheduleStatus} from "ecco-components";
import {GoalUpdateCommandDto} from "ecco-dto/evidence-dto";
import {Change} from "ecco-dto/command-dto";
import RecordScheduleControl from "../../controls/RecordScheduleControl";
import {eccoDateAsElement} from "@eccosolutions/ecco-mui-controls";
import {ScheduleData} from "ecco-rota";

function to<T>(change: Change<T>): T | undefined {
    return change && change.to;
}

/**
 * Individual representation of a smart st
 */
class SupportInstanceControl extends BaseActionInstanceControl implements ActionInstanceControl {

    private $containerLi: $.JQuery;
    private $containerPanel: $.JQuery;
    private $containerOuter: $.JQuery;
    private $containerInnerSummary: $.JQuery;
    private $containerInnerContent: $.JQuery;

    private showEvidenceAsGoals: boolean;
    private showTargetScheduleControl: boolean;

    // original components, but used in 2 methods
    private goalName: TextInput = null;
    private goalNameText: $.JQuery = null;
    private actionDefName: $.JQuery = null;
    private activityDefList: SelectList;
    private scoreListIdLookup: (score: number) => number | undefined;
    private scoreListDisplayLookup: (score: number) => string | undefined;
    private scoreList: SelectList;
    private scoreListGroup: InputGroup; // for validation
    private scorePrev: $.JQuery = null;
    private activityDefGroup: InputGroup;
    private statusControl: SmartStepStatusControl;
    private relatedControl: ClickableImageControl;
    private expiryDateIcon: $.JQuery = null;
    private expiryDateControl: $.JQuery;
    private targetDateIcon: $.JQuery = null;
    private targetDateControl: $.JQuery;
    private scheduleControl: Element = null;
    private recordScheduleControl: RecordScheduleControl = null;
    private actionGoalPlanControl: TextAreaInput;
    private hideStatusChangeReasonGroup = false;
    private statusChangeReasonGroup: InputGroup; // for validation
    private statusChangeReasonControl: SelectList;
    private instanceState: ActionInstanceState;

    /**
     * Create a control for the smart step.
     */
    constructor(context: EvidenceContext, sessionData: SessionData, serviceRecipientId: number,
                evidenceDef: EvidenceDef, actionDef: ActionComponent,
                initialData: ActionInstanceControlData, controlUuid: Uuid,
                controlFeatures: ActionInstanceFeatures) {
        super(context, sessionData, serviceRecipientId, evidenceDef, actionDef, initialData, controlUuid, controlFeatures);
        this.instanceState = new ActionInstanceState(initialData.targetDate, initialData.targetSchedule);
    }

    protected initialise() {
        this.$containerLi = $("<li>").addClass("smartstep");
        this.$containerPanel = $("<div>").addClass("panel").addClass("panel-default");

        this.showEvidenceAsGoals = this.context.configResolver.getServiceType()
            .getTaskDefinitionSetting(this.context.evidenceDef.getTaskName(),  "showEvidenceStyleAs") == "goals";

        this.$containerOuter = this.showEvidenceAsGoals ? $("<div>").addClass("panel-body").appendTo(this.$containerPanel) : this.$containerLi;
        if (this.showEvidenceAsGoals) {
            this.$containerLi.append(this.$containerPanel);
        }

        this.showTargetScheduleControl = this.controlFeatures.showTargetSchedule(this.actionDef.getOutcome().getId());

        // prime with the given data
        //updateActionInstanceUuid - just use this.uuid()
        this.updateStatusState(this.initialData.status, false);
        this.updateTargetState(this.initialData.targetDate);
        this.updateTargetScheduleState(this.initialData.targetSchedule);
        this.updateExpiryState(this.initialData.expiryDate);
        this.updateGoalNameState(this.initialData.goalName);
        this.updateGoalPlanState(this.initialData.goalPlan);
        this.updateScoreState(this.initialData.score);
        this.updateActivityState(this.initialData.activityState);
        this.updateHierarchyState(this.initialData.hierarchy);
        this.updatePositionState(this.initialData.position);
        this.updateChangeReasonState(this.initialData.statusChangeReasonId);
    }

    protected draw() {
        if (this.context.asForwardCarePlan) {this.drawAsForwardCarePlan()}
        else if (this.context.asForwardPlan || this.context.asForwardRiskPlan) {this.drawAsForwardPlan()}
        else if (this.controlFeatures.isAllowFromBlankUi()) {throw new Error("allowFromBlankUi is unsupported")}
        else {this.drawNew()}
    }

    protected drawNew() {
        this.getContainer().empty();

        this.createControls();
        this.renderControls();
    }

    protected createControls() {
        const tapToEdit = this.context.features.isEnabled("support.evidence.tapToEditTextAreas");
        const outcomeId = this.actionDef.getOutcome().getId();
        const messages = this.context.features.getMessages()

        // STATUS CONTROL
        // we need the control even if never added to the page - so create it early
        this.statusControl = new SmartStepStatusControl(this.latest.status, this.context.statusTransitions,
            () => this.nextStatusUI());

        // RELATED/CHAIN/LINK
        // NB whilst a related/link/chain is actually configurable via a 'referral aspect setting'
        // it is configured on every instance, so we always display it
        if (this.controlFeatures.showLink(outcomeId)) {
            this.relatedControl = new ClickableImageControl(this.relatedSrc(this.currentRelated),
                () => this.updateRelatedUI(true));
        }

        // EXPIRY DATES
        if (this.controlFeatures.showExpiry(outcomeId)) {

            this.expiryDateControl = $("<span>").append(eccoDateAsElement({
                label: "expiry date",
                onChange: date => this.updateExpiryUI(date ? date.formatIso8601() : null),
                value: EccoDate.parseIso8601(this.latest.expiryDate)
            }));

            this.expiryDateIcon = $("<span>").addClass(`clickable-image ${this.dateImage}`)
                    .attr("role", "img")
                    .attr("title", "expiry date")
                    .html("&nbsp;")
                    .click(() => {
                        this.expiryDateControl.show();
                        // this.dateIcon.hide();
                    });
        }

        // TARGET DATES
        if (this.controlFeatures.showTarget(outcomeId)) {

            // for now assume validation means required
            const required = this.controlFeatures.validateActionComponents(outcomeId, 'targetDate');

            this.targetDateControl = $("<span>").append(eccoDateAsElement({
                required: required,
                label: "target date",
                onChange: date => this.updateTargetUI(date ? date.formatIso8601() : null),
                value: EccoDateTime.parseIso8601(this.latest.targetDateTime)?.toEccoDate() || null
            }));

            this.targetDateIcon = $("<span>").addClass(`clickable-image ${this.dateImage}`)
                .attr("role", "img")
                .attr("title", "target date")
                .html("&nbsp;")
                .click(() => {
                    this.targetDateControl.show();
                    // this.dateIcon.hide();
                });

        }

        // TARGET SCHEDULE
        if (this.showTargetScheduleControl) {
            const scheduleData = ScheduleData.fromTargetSchedule(EccoDateTime.parseIso8601(this.instanceState.targetDate) || null,
                this.instanceState.targetSchedule);
            this.scheduleControl = CronScheduleStatus.asElement({
                allowMultipleTimes: true,
                readOnly: false,
                title: "target schedule",
                instanceState: scheduleData,
                onChange: (data) => {
                    this.instanceState.targetSchedule = data.getScheduleForCmd();
                    return this.updateTargetScheduleUI(data.getStart() && data.getStart().formatIso8601(), data.getScheduleForCmd());
                }
            });
        }

        if (this.showTargetScheduleControl && this.evidenceDef.getEvidencePageType() == EvidencePageType.reduction) {
            this.recordScheduleControl = new RecordScheduleControl(this.context, this.actionDef, this.controlUuid);
        }

        // SCORE
        if (this.controlFeatures.showScore(outcomeId)) {
            this.scoreList = new SelectList(`score-${this.controlUuid.toString()}`).withEmptyEntryValue("");
            const scoreListDef = this.context.configResolver.getServiceType().getTaskDefinitionSetting(this.context.evidenceDef.getTaskName(), "scoreListName");
            let latestScore = this.latest.score;
            if (this.controlFeatures.clearActionComponents(outcomeId, 'score')) {
                latestScore = null;
            }
            if (scoreListDef) {
                const lst = this.context.features.getListDefinitionEntriesByListName(scoreListDef);
                this.scoreListIdLookup = (score: number) => lst.filter(i => score == i.getId())?.pop()?.getId();
                this.scoreListDisplayLookup = (score: number) => lst.filter(i => score == i.getId())?.pop()?.getDisplayName();
                this.scoreList.populateFromList(lst,
                        (item) => ({key: item.getId().toString(), value: item.getDisplayName(), isHidden: item.getDisabled(), isDefault: item.getDefault()}),
                        (item) => item.getId() == latestScore);
            } else {
                this.scoreListIdLookup = (score: number) => score;
                this.scoreListDisplayLookup = (score: number) => score?.toString() || "";
                this.scoreList.populateFromList([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
                        (item) => ({key: String(item), value: String(item), isDefault: false}),
                        (item) => item == latestScore);
            }
            this.scoreListGroup = new InputGroup("", this.scoreList, undefined, true);
            const validation = (valid) => {
                if (valid) {
                    const entry = this.scoreList.val();
                    const score = entry == "" ? null : Number(entry);
                    this.updateScoreUI(score);
                }
            }
            if (this.controlFeatures.validateActionComponents(outcomeId, 'score')) {
                this.scoreListGroup.withCallback(validation);
                this.scoreListGroup.enableValidation();
            } else {
                // if '-' this does work, but doesn't actually show in the history
                this.scoreList.change(value => {
                    validation(true)
                })
            }
            this.scoreListGroup.element().css("vertical-align", "bottom");
        }

        // GOAL NAME (name)
        // LIMIT 384 characters
        if (this.controlFeatures.showGoalName(outcomeId)) {
            this.goalName = new TextInput(undefined, messages[`${this.evidenceDef.getTaskName()}.goalName.label`] || messages["goalName.label"]);
            this.goalName.withMaxLength(384);
            this.goalName.setVal(this.latest.goalName);
            this.goalName.change((value) => this.updateNameUI(value), true);
            this.$goalName.empty();
            this.$goalName.append(this.goalName.element());
        }

        // GOAL PLAN (comment)
        if (this.controlFeatures.showGoalPlan(outcomeId)) {
            this.actionGoalPlanControl = new TextAreaInput(null, 3, tapToEdit);
            this.actionGoalPlanControl.placeholderText(messages[`${this.evidenceDef.getTaskName()}.goalPlan.label`] || messages["goalPlan.label"]);
            this.actionGoalPlanControl.setVal(this.latest.goalPlan);
            this.actionGoalPlanControl.change((value) => this.updateCommentUI(value), true);
            this.$goalPlan.empty();
            this.$goalPlan.append(this.actionGoalPlanControl.element());
        }

        // ACTIVITY INTEREST
        if (this.controlFeatures.showActivityInterest(outcomeId)) {
            this.createActivityInterest();
        }

        // statusChangeReasonControl
        if (this.controlFeatures.showStatusChangeReason(outcomeId)) {
            this.createStatusChangeReasonControl();
        }

    }

    protected renderControls() {

        this.$containerInnerSummary = $("<div>");
        this.$containerInnerContent = $("<div>");

        this.getContainer()
            .append(this.$containerInnerSummary)
            .append(this.$containerInnerContent);

        const $actionDefText = $("<span>")
            .css({'font-style' : 'italic', 'text-size' : '0.7em'})
            .append(this.controlFeatures.showTitle() ? this.getActionDefName(false) : null)
            .attr("data-actiondefid", this.getActionDef().getId());
        const $actionDefRow = $("<div>").addClass("row").append(
                $("<div>").addClass("col-xs-12").append($actionDefText));
        this.$containerInnerSummary
            .append($actionDefRow);

        // status is needed, but can be hidden
        const outcomeId = this.actionDef.getOutcome().getId();
        const showStatus = this.controlFeatures.showStatus(outcomeId);
        const showLink = this.controlFeatures.showLink(outcomeId);

        // if goalName then create a first row with status/link/score/goalName
        if (this.goalName) {
            const leftWidth = this.statusControl && this.relatedControl && showStatus && showLink ? "10%" : "5%";
            const rightWidth = this.statusControl && this.relatedControl && showStatus && showLink ? "90%" : "94%";
            const $nameRowLeft = $("<div>").css({"width": leftWidth, "float": "left"});
            this.statusControl && showStatus && $nameRowLeft.append(this.statusControl.element());
            this.relatedControl && showLink && $nameRowLeft.append(this.relatedControl.element());
            const $nameRowRight = $("<div>").css({"width": rightWidth, "float": "right"});
            this.goalName.element().css("width", "100%"); // text input needs to fill its container
            $nameRowRight.append(this.$goalName);
            const $nameRow = $("<div>").addClass("col-xs-12").append($nameRowLeft).append($nameRowRight);
            this.$containerInnerSummary
                .append($("<div>").addClass("row").append($nameRow));
            // if no goal name text then hide
            if (!this.latest.goalName && !this.latest.status) {
                this.goalName.element().hide();
            }
        }

        const $targetDteRow = $("<div>").addClass("col-xs-12").css({"margin-top": "8px"});

        // if not goalName then add the status/link/score to this line
        if (!this.goalName) {
            this.statusControl && showStatus && $targetDteRow.append(this.statusControl.element());
            this.relatedControl && showLink && $targetDteRow.append(this.relatedControl.element());
        }

        if (this.scoreList) {
            $targetDteRow.append(this.scoreListGroup.element());
            // hide icon if no score and no status (ensures we don't hide if just not entered value, but is relevant)
            // except in cases where we want to force the user to fill every smart step
            const force = this.controlFeatures.forceActionComponents(outcomeId, 'score');
            const hide = !this.latest.score && !this.latest.status && !force;
            if (hide) {
                this.scoreListGroup.element().hide();
            }

            if (this.latest.score && this.controlFeatures.clearActionComponents(outcomeId, 'score')) {
                const scoreDisplay = this.scoreListDisplayLookup(this.latest.score);
                this.scorePrev = $("<span>")
                        .css({"vertical-align": "bottom",
                            "margin-right": "10px",
                            "opacity": "0.54"})
                        .text(" was ")
                        .append(
                            $("<button>")
                                .text(scoreDisplay)
                        );
                this.scorePrev.click(() => {
                    this.updateScoreToUI(this.latest.score);
                })
                $targetDteRow.append(this.scorePrev);
            }

        }

        if (this.targetDateControl) {
            $targetDteRow.append(this.targetDateIcon);
            $targetDteRow.append(this.targetDateControl);

            if (!this.showTargetDateControl()) {
                this.targetDateControl.hide();
            }
            if (!this.showTargetDateIcon()) {
                this.targetDateIcon.hide();
            }

            // prioritise the targetSchedule if it exists
            if (this.showTargetScheduleControl && this.latest.targetSchedule) {
                this.targetDateControl.hide();
                this.targetDateIcon.hide();
            }
        }

        if (this.expiryDateControl) {
            $targetDteRow.append(this.expiryDateIcon);
            $targetDteRow.append(this.expiryDateControl);
            if (!this.latest.expiryDate) {
                this.expiryDateControl.hide();
                // hide icon if no status
                if (!this.latest.status) {
                    this.expiryDateIcon.hide();
                }
            }
        }

        // NB if targetSchedule, then we hide 'statusControl' on reduction pages (see 42ff1d9d and 53f15a1a)
        // BUT we should just be able to untick 'status' in the configuration of checklists now
        if (this.scheduleControl) {
            $targetDteRow.append(this.scheduleControl);
            // hide until its made relevant with a status
            if (!this.latest.targetSchedule && !this.latest.status) {
                $(this.scheduleControl).hide();
            }
        }

        const $recordScheduleRow = $("<div>").addClass("col-xs-12").css({"margin-top": "8px"});
        if (this.showTargetScheduleControl && this.recordScheduleControl && this.latest.targetSchedule) {
            $recordScheduleRow.append(this.recordScheduleControl.element());
        }

        if (!showStatus) {
            this.statusControl && this.statusControl.element().hide();
        }
        if (!showLink) {
            this.relatedControl && this.relatedControl.element().hide();
        }

        this.$containerInnerContent
            .append($("<div>").addClass("row").append($targetDteRow));
        this.$containerInnerContent
            .append($("<div>").addClass("row").append($recordScheduleRow));

        if (this.actionGoalPlanControl) {
            // if no goal plan text or active status, then hide
            if (!this.latest.goalPlan && !this.latest.status) {
                const ignoreStatus = this.context.features.isEnabled("support.evidence.goalPlanIgnoresStatus");
                if (!ignoreStatus) {
                    this.actionGoalPlanControl.element().hide();
                }
            }
            const $goalPlanRow = $("<div>").addClass("row")
                .append($("<div>").addClass("col-xs-12").append(this.$goalPlan));
            this.$containerInnerContent
                .append($goalPlanRow);
        }

        if (this.activityDefGroup) {
            // if no activity or active status, then hide
            // TODO should probably migrate to this.latest.activityState
            if (this.initialData.activityState.length == 0 && !this.latest.status) {
                this.activityDefGroup.element().hide();
            }
            this.$containerInnerContent.append(this.activityDefGroup.element());
        }

        if (this.statusChangeReasonControl) {
            this.$containerInnerContent.append(this.statusChangeReasonGroup.element());
            if (this.hideStatusChangeReasonGroup) {
                this.statusChangeReasonGroup.element().hide();
            }
        }

        this.getContainer().append($("<div>").css({'padding-top':'15px'}));
    }

    private drawAsForwardCarePlan() {
        // draw the support instance as normal
        // we just don't have the comment area, tabs etc
        this.drawNew();
    }

    private drawAsForwardPlan() {
        // no change if we actually don't have a target date (triggered event may not have target date)
        if (!EccoDateTime.parseIso8601(this.latest.targetDateTime)) {
            return;
        }

        const $containerInner = this.getDrawContainer();
        $containerInner.empty();

        let actionGoalName: $.JQuery;
        let actionGoalPlan: $.JQuery;
        let $target, $expiry: $.JQuery;

        // EXPIRY
        const expiryDate = EccoDate.parseIso8601(this.latest.expiryDate);
        if (expiryDate) {
            $expiry = $("<p>").text(expiryDate.formatShortWithDay());
            if (expiryDate.earlierThan(EccoDate.todayLocalTime())) {
                $expiry.css("color", "#c00");
            }
        }

        // TARGET
        const targetDate = EccoDateTime.parseIso8601(this.latest.targetDateTime)?.toEccoDate() || null;
        $target = $("<p>").text(targetDate.formatShortWithDay());
        if (targetDate.earlierThan(EccoDate.todayLocalTime())) {
            $target.css("color", "#c00");
        }

        const $targetOrExpiry = expiryDate && targetDate
                ? expiryDate.earlierThan(targetDate) ? $expiry : $target
                : expiryDate ? $expiry : $target

        // NAME - GOALNAME
        const showGoalName = this.controlFeatures.showGoalName(this.actionDef.getOutcome().getId())
                && !!this.latest.goalName;
        if (showGoalName) {
            actionGoalName = $("<p>").css('font-weight', 'bold');
            actionGoalName.text(this.latest.goalName);
        }

        // COMMENT - GOALPLAN
        const settingFwd = this.context.asForwardPlan ? "forwardPlanTab" : "forwardRiskPlanTab";
        let hideGoalPlan = this.context.configResolver.getServiceType().taskDefinitionSettingHasFlag(TaskNames.referralView, settingFwd, "hideGoalPlan")
        const showGoalPlan = !hideGoalPlan && this.controlFeatures.showGoalPlan(this.actionDef.getOutcome().getId())
                && !!this.latest.goalPlan;
        if (showGoalPlan) {
            actionGoalPlan = $("<p>");
            actionGoalPlan.text(this.latest.goalPlan);
        }

        // ACTION DEF NAME
        if (this.actionDef) {
            this.actionDefName = $("<p>").attr("data-actiondefid", this.actionDef.getId());
            let name = '['.concat(this.actionDef.getOutcome().getName()).concat('] ').concat(this.actionDef.getName());
            this.actionDefName.text(name);
            if (this.latest.goalName) {
                this.actionDefName.css("font-size", "smaller");
            }
        }

        const row1Goal = showGoalName ? actionGoalName : showGoalPlan ? actionGoalPlan : undefined
        const row2Goal = (showGoalName && showGoalPlan) ? actionGoalPlan : undefined
        const $rowContainer = $("<div>").addClass("container-fluid");
        const $row1 = row1Goal && $("<div>").addClass("row")
                .append(
                        $("<div>").addClass("col-xs-3").append($targetOrExpiry)
                )
                .append(
                        $("<div>").addClass("col-xs-9").append(row1Goal.css("margin-bottom", "0px"))
                );
        const $row2 = row2Goal && $("<div>").addClass("row")
                .append(
                        $("<div>").addClass("col-xs-3").html("&nbsp;")
                )
                .append(
                        $("<div>").addClass("col-xs-9").append(row2Goal.css("margin-top", "-10px"))
                );
        const $row3 = $("<div>").addClass("row")
            .append(
                $("<div>").addClass("col-xs-9").append($("<span>").css("font-size", "0.8em").append(this.actionDefName))
            );

        $rowContainer.append($row1).append($row2).append($row3);
        $containerInner.append($rowContainer);
    }

    private createActivityInterest() {
        this.activityDefList = new SelectList(null, false);
        this.activityDefList.element().css("width", "100%");
        this.activityDefList.element().attr("multiple", "multiple");

        this.activityDefGroup = new InputGroup("", this.activityDefList);
        //this.activityDefGroup.element().hide();
        // select2 declared in require-boot as a shim for this module
        (<any>$("select", this.activityDefGroup.element())).select2({
            placeholder: "optional group activities"
        });

        $("label", this.activityDefGroup.element()).hide();

        if (!this.initialData.asCreatingFromEmpty) {
            this.refreshActivityList();
        }

        this.activityDefList.change((idSelectedArr) => {
            this.updateActivityState(<number[]><any>idSelectedArr);
        });
    }

    private createStatusChangeReasonControl() {
        this.statusChangeReasonControl = new SelectList(`change-${this.controlUuid.toString()}`).withEmptyEntryValue("");

        //const listDef = this.context.configResolver.getServiceType().getTaskDefinitionSetting(this.context.evidenceDef.getTaskName(), "actionNotCompleteListName");
        const listName = "actionNotCompleteReason";
        const initValue = this.latest.statusChangeReasonId;

        const listDef = this.context.features.getListDefinitionEntriesByListName(listName);
        this.statusChangeReasonControl.populateFromList(listDef,
                    (item) => ({key: item.getId().toString(), value: item.getDisplayName(), isHidden: item.getDisabled(), isDefault: item.getDefault()}),
                    (item) => item.getId() == initValue);

        this.statusChangeReasonGroup = new InputGroup("reason", this.statusChangeReasonControl, undefined, true);
        const validation = (valid) => {
            if (valid) {
                const entry = this.statusChangeReasonControl.val();
                const value = entry == "" ? null : Number(entry);
                this.updateStatusChangeReasonUI(value);
            }
        }
        this.hideStatusChangeReasonGroup = !this.latest.status;
        this.statusChangeReasonGroup.withCallback(validation);
        if (!this.hideStatusChangeReasonGroup) {
            this.statusChangeReasonGroup.enableValidation();
        }
        this.statusChangeReasonGroup.element().css("vertical-align", "bottom");
    }

    private getActionDefName(withOutcomeName: boolean): $.JQuery {
        if (!this.getActionDef()) return $("<span>");
        const prefix = withOutcomeName
            ? '['.concat(this.getActionDef().getOutcome().getName()).concat('] ')
            : "";
        let postfix = "";
        if (this.context.features.isEnabled("support.evidence.showActionIdsToggle")
            && this.sessionData.hasRoleEvidenceAdmin()) {
            const uuid = this.initialActionInstanceUuid() ? " ".concat(this.initialActionInstanceUuid()) : "";
            const parentUuid = this.parentUuid() ? " ".concat(this.parentUuid()) : "";
            postfix = uuid + " parent:" + parentUuid;
        }
        const $name = $("<span>");
        $name.append($("<span>").addClass("actionDefName").text(prefix.concat(this.getActionDef().getName())));
        $name.append($("<span>").text(postfix).addClass("small"));
        return $name;
    }

    public applyCommands(commandQueue: CommandQueue): Promise<void> {
        throw new Error("unimplemented")
    }

    protected updateStateFromSnapshot(data: ActionInstanceControlData) {
        // TODO should also update ui controls - see applyState
        this.updateStatusState(data.status, false);
        this.updateTargetState(data.targetDate);
        this.updateTargetScheduleState(data.targetSchedule);
        this.updateExpiryState(data.expiryDate)
        this.updateGoalNameState(data.goalName);
        this.updateGoalPlanState(data.goalPlan);
        this.updateScoreState(data.score);
        this.updateChangeReasonState(data.statusChangeReasonId);
        //this.updateActivityState()
        //this.updateRelatedState()
        this.updatePositionState(data.position);
        this.updateHierarchyState(data.hierarchy);
    }

    /**
     * Called from applyCommand which is used in emitCommands.
     * Not entirely sure this is called - it seems to exist simply from a refactor 3248d173.
     */
    protected updateStateFromCommand(dto: GoalUpdateCommandDto) {
        // TODO should also update ui controls - see applyState
        this.updateStatusState(to(dto.statusChange), false);
        this.updateTargetState(to(dto.targetDateChange));
        this.updateTargetScheduleState(to(dto.targetScheduleChange));
        this.updateExpiryState(to(dto.expiryDateChange));
        this.updateGoalNameState(to(dto.goalNameChange));
        this.updateGoalPlanState(to(dto.goalPlanChange));
        this.updateScoreState(to(dto.scoreChange));
        //this.statusChangeReasonId
        //this.updateActivityState()
        //this.updateRelatedState()
        this.updatePositionState(to(dto.positionChange));
        this.updateHierarchyState(to(dto.hierarchyChange));
    }

    private refreshActivityList() {
        let actTypeOnActionDef = this.getActionDef().getActivityTypes();
        let actTypeInterestIds = this.context.serviceRecipientActivityInterest.map((actType) => actType.id);
        this.activityDefList.populateFromList(actTypeOnActionDef.sort((a, b) => a.name.localeCompare(b.name)),
            (activity: ActivityType) => ({key: activity.id.toString(), value: activity.name}),
            (activity) => actTypeInterestIds.indexOf(activity.id) > -1);
    }

    private showTargetDateIcon() {
        const outcomeId = this.actionDef.getOutcome().getId();
        // if validate, we always show the input - so never show the icon
        const validate = this.controlFeatures.validateActionComponents(outcomeId, 'targetDate');
        // if forced, we always show the input - so never show the icon
        const force = this.controlFeatures.forceActionComponents(outcomeId, 'targetDate');
        return this.targetDateIcon && !(force || validate) && this.latest.status && !this.latest.targetDateTime;
    }
    private showTargetDateControl() {
        const outcomeId = this.actionDef.getOutcome().getId();
        const force = this.controlFeatures.forceActionComponents(outcomeId, 'targetDate');
        return this.targetDateControl && (force || this.latest.status);
    }

    protected showComponents(fadeIn = 500) {
        this.goalName && this.goalName.element().fadeIn(fadeIn);
        this.goalNameText && this.goalNameText.fadeIn(fadeIn);
        this.actionDefName && this.actionDefName.fadeIn(fadeIn);
        this.activityDefGroup && this.activityDefGroup.element().fadeIn(fadeIn);

        // status needs to exist currently, but its not attached to any dom if not showStatus
        this.statusControl && !this.initialData.asCreatingFromEmpty && this.statusControl.element().fadeIn(fadeIn);
        this.relatedControl && !this.initialData.asCreatingFromEmpty && this.relatedControl.element().fadeIn(fadeIn);

        this.scoreList && this.scoreListGroup.element().fadeIn(fadeIn);
        this.scorePrev && this.scorePrev.fadeIn(fadeIn);

        if (this.showTargetDateIcon()) {
            this.targetDateIcon.fadeIn(fadeIn);
        }
        if (this.showTargetDateControl()) {
            this.targetDateControl.fadeIn(fadeIn);
        }
        this.scheduleControl && $(this.scheduleControl).fadeIn(fadeIn);

        if (this.expiryDateIcon) {
            this.expiryDateIcon.fadeIn(fadeIn);
            this.latest.expiryDate && this.expiryDateControl.fadeIn(fadeIn);
        }

        this.actionGoalPlanControl && this.actionGoalPlanControl.element().fadeIn(fadeIn);
        this.statusChangeReasonControl && !this.hideStatusChangeReasonGroup && this.statusChangeReasonGroup.element().fadeIn(fadeIn);
    }

    public emitChangesTo(commandQueue: CommandQueue) {
        const op = this.initialActionInstanceUuid() ? "update" : "add";
        const parentUuid: Uuid = this.parentUuid() ? Uuid.parse(this.parentUuid()) : null;
        const cmdUuid = Uuid.randomV4();

        const cmd = new GoalUpdateCommand(op, cmdUuid, this.context.getWorkUuid(), this.context.serviceRecipientId,
                    this.context.evidenceDef.getTaskName(), this.getActionDef().getId(), this.context.evidenceDef.getEvidenceGroup(),
                    Uuid.parse(this.actionInstanceUuidOrControlUuid()), parentUuid);
        this.populateGoalUpdateCommand(cmd);

        commandQueue.addCommand(cmd);

        if (this.initialData.activityState.length > 0 || this.currentActivityState.length > 0) {

            const nowOnWasOff = diff(this.currentActivityState, this.initialData.activityState);
            const nowOffWasOn = diff(this.initialData.activityState, this.currentActivityState);

            nowOnWasOff.forEach(activityId => {
                const cmd = new ActivityInterestChangeCommand("add",
                    this.context.serviceRecipientId, activityId);
                commandQueue.addCommand(cmd);
            });

            nowOffWasOn.forEach(activityTypeId => {
                const cmd = new ActivityInterestChangeCommand("remove",
                    this.serviceRecipientId, activityTypeId);
                commandQueue.addCommand(cmd);
            });

        }

    }

    /**
     * Work out what has changed.
     * Ideally we use initialData vs latest, however, it may be that the last control doesn't trigger a 'change'.
     * What IS important though is to not assume the control exists, and record a change when there is none
     * TODO see if the last control 'change' is triggered - so we can use initialData vs latest (and not direct control inspection)
     */
    protected populateGoalUpdateCommand(cmd: GoalUpdateCommand) {

        this.instanceState.populateGoalUpdateCommand(cmd);

        // if the user clicks 'star' and 'AchievedAndStillRelevant' we should prioritise the star
        const prioritiseStatus = this.statusControl && (asNumberChange(this.initialData.status, this.statusControl.getStatus()) != undefined);
        if (!prioritiseStatus && this.showTargetScheduleControl && this.recordScheduleControl) {
            // don't record '5' to 'null' - since checks aren't meant to send anything if not recorded
            if (this.recordScheduleControl.getStatus()) {
                cmd.changeStatus(this.initialData.status, this.recordScheduleControl.getStatus());
                if (this.recordScheduleControl.getStatus() == SmartStepStatus.AchievedAndStillRelevant) {
                    cmd.setForceStatusChange();
                }
            }
            cmd.changeStatusChangeReason(this.initialData.statusChangeReasonId, this.recordScheduleControl.getStatusChangeReason());
        } else {
            if (this.statusControl) {
                cmd.changeStatus(this.initialData.status, this.statusControl.getStatus());
            }
            if (this.statusChangeReasonControl) {
                cmd.changeStatusChangeReason(this.initialData.statusChangeReasonId, this.latest.statusChangeReasonId);
            }
        }

        if (this.currentRelated) {
            cmd.setRelevant();
        }

        cmd.changeExpiryDate(this.initialData.expiryDate, this.latest.expiryDate)

        cmd.changeGoalName(this.initialData.goalName, this.latest.goalName);

        cmd.changeScore(this.initialData.score, this.latest.score);

        cmd.changeHierarchy(this.initialData.hierarchy, this.latest.hierarchy);

        cmd.changePosition(this.initialData.position, this.latest.position);

        // LAST control to check needs to be this
        if (this.actionGoalPlanControl) {

            // TEMPORARY HACK PART 1/2
            // (needed until multiple smart steps are in place)
            // Works around the misuse of an unknown legacy 'feature' where changing
            // the comment box only did not trigger a 'relevant' smart step - just a
            // comment in the history against the smart step.
            // To isolate this logic as much as possible, we restrict the hack to the
            // client side where it is used. We can fix this by setting the status
            // to 'not relevant' IF no other changes have been made.
            let hasOtherChanges = cmd.hasChanges();

            // ORIGINAL BEHAVIOUR
            cmd.changeGoalPlan(this.initialData.goalPlan, this.actionGoalPlanControl.val());

            // TEMPORARY HACK PART 2/2
            // if we have a comment change but not any other changes
            // then set the status as not relevant - if we aren't anything already
            let hasStatus = this.statusControl.getStatus();
            // ONLY if the feature is set to ignore
            let ignoreStatus = this.context.features.isEnabled("support.evidence.goalPlanIgnoresStatus");
            if (ignoreStatus && !hasOtherChanges && cmd.hasChanges() && !hasStatus) {
                // NoLongerWanted is the only status we can be if we don't already
                // have a status.
                // changeStatus is the bit which actually does the HACK
                cmd.changeStatus(null, SmartStepStatus.NoLongerWanted);
            }
        }
    }

    public element(): $.JQuery {
        if (this.controlFeatures.isAllowFromBlankUi()) return this.$containerOuter;
        if (this.context.asForwardCarePlan) return super.element();
        if (this.context.asForwardPlan || this.context.asForwardRiskPlan) {return this.getDrawContainer();}
        return super.element();
    }

    public domElement() {
        return this.element()[0];
    }

    public getContainer(): $.JQuery {
        return this.controlFeatures.isAllowFromBlankUi() ? this.$containerOuter : super.getContainer();
    }
    protected getDrawContainer(): $.JQuery {
        return this.$containerLi;
    }

    public isRelevant() {
        return this.statusControl
            ? this.statusControl.getStatus() ? this.statusControl.getStatus() != SmartStepStatus.NoLongerWanted : false
            : false;
    }

    public isAchieved() {
        return this.statusControl && SmartStepStatus.Achieved == this.statusControl.getStatus();
    }

    public showSummaryOnly() {
        this.$containerInnerContent.hide();
        this.$containerInnerSummary.click(() => this.$containerInnerContent.fadeIn());
    }

    /** change the status of this smart step (in the ui and the state) */
    public nextStatusUI(): void {
        // status state update
        const status = this.context.statusTransitions.getNextStatus(this.initialData.status, this.latest.status);
        // if no status can be changed - skip processing
        if (status == this.latest.status) {
            return;
        }
        this.updateStatusState(status, true);
        this.refreshStatusUI();
    }

    private listOfActionDefs(): ServiceTypeElement[] {
        const outcomes: Outcome[] = this.context.serviceRecipientWithEntities.configResolver
            .getOutcomesFilteredForTask(this.context.evidenceDef.getTaskName());
        const actionGroups = _.flatten(outcomes.map((outcome) => outcome.actionGroups));
        const actions = _.flatten(actionGroups.map((actionGroup) => actionGroup.actions));
        return actions.map(actionDef => {
            const smartstepById = this.lookupSmartStepDefinition(actionDef.getId());
            return new ServiceTypeElement(actionDef.getId(),
                '['.concat(actionDef.actionGroup.outcome.getName()).concat('] ').concat(actionDef.getName()),
                smartstepById.isDisabled());
        });
    }

    // find the support/risk defn - also see serviceType.getAnyActionById
    protected lookupSmartStepDefinition(id: number): ServiceTypeElement {
        return this.context.features.getSupportActionById(id);
    }

    private refreshStatusUI() {
        this.statusControl.setStatus(this.latest.status);

        if (this.statusChangeReasonControl) {
            // changed to achieve.. then stop incomplete reason
            if (this.latest.status == SmartStepStatus.Achieved
                    || this.latest.status == SmartStepStatus.NoLongerWanted
                    || this.latest.status == SmartStepStatus.CommentOnly) {
                this.statusChangeReasonGroup.disableValidation();
                this.latest.statusChangeReasonId = null;
                this.hideStatusChangeReasonGroup = true;
                this.statusChangeReasonGroup.element().hide();
            } else {
                // ideally we have an ensureValidation (as enableValidation is designed as one-off handlers)
                // but we also need to trigger the validation, which we may as well do here
                this.statusChangeReasonGroup.element().show();
                this.statusChangeReasonGroup.ensureValidation();
                this.statusChangeReasonGroup.triggerValidation();
                this.hideStatusChangeReasonGroup = false;
            }
        }

        // check related ui and state change
        this.updateRelatedUI(false);
    }

    protected refreshIntervention() {}

    /** change the 'related/relevant/linked/chain' of this smart step
     * @param directClick Whether the link/chain button was clicked on */
    public updateRelatedUI(directClick: boolean): void {
        // ensure the related state is consistent after other possible state changes
        this.updateRelatedState(directClick);
        // update the ui
        if (this.relatedControl) {
            this.relatedControl.setSrc(this.relatedSrc(this.currentRelated));
        }
/*
        if (this.statusChangeReasonControl) {
            if (this.latest.status == SmartStepStatus.Achieved) {
                this.statusChangeReasonGroup.disableValidation();
            }
            // ideally we have an ensureValidation (as enableValidation is designed as one-off handlers)
            // but we also need to trigger the validation, which we may as well do here
            //this.statusChangeReasonGroup.triggerValidation();
        }
*/

        if (!directClick) {
            this.ensureActionComponentsVisible();
        }
    }

    protected ensureActionComponentsVisible() {
        // any 'touched' part of the smart step and we should show components, if they are hidden by default
        // NB we should perhaps trigger GoalTransientStatusEvent here, but its not potentially not a status change yet (eg target date change)
        // TODO updates funnel through updateRelatedUI but would be cleaner using state (or triggering 'GoalTouchedEvent' to handle check/showComponents)
        this.showComponents(0);
        events.GoalTouchedEvent.bus.fire(
            new events.GoalTouchedEvent(this.actionInstanceUuidOrControlUuid().toString()));
    }

    public updateExpiryUI(newDate: string) {
        this.updateExpiryState(newDate);
        this.updateRelatedUI(false);
    }

    /** sets an action on a smart step */
    public updateTargetUI(newDate: string) {
        this.updateTargetState(newDate);
        this.updateRelatedUI(false);
    }

    public updateTargetScheduleUI(scheduleStart: string, schedule: string) {
        this.updateTargetState(scheduleStart);
        this.updateTargetScheduleState(schedule);
        this.toggleTargetDate(!!schedule);
        this.updateRelatedUI(false);
    }

    /** sets a goal name on an individual smart step */
    public updateNameUI(newName: string) {
        this.updateGoalNameState(newName);
        this.updateRelatedUI(false);
    }

    /** sets a goal/comment on an individual smart step */
    public updateCommentUI(newComment: string) {
        this.updateGoalPlanState(newComment);
        this.updateRelatedUI(false);
    }

    /** sets a score on an individual smart step */
    public updateScoreUI(newScore: number) {
        this.updateScoreState(newScore);
        this.updateRelatedUI(false);
    }
    public updateScoreToUI(newScore: number) {
        this.updateScoreState(newScore);
        this.updateRelatedUI(false);
        this.scoreList.setVal(this.scoreListIdLookup(newScore)?.toString());
    }

    public updateStatusChangeReasonUI(newValue: number) {
        this.updateChangeReasonState(newValue);
        this.updateRelatedUI(false);
    }

    private toggleTargetDate(hide: boolean) {
        if (!this.targetDateIcon) {
            return;
        }
        if (hide) {
            this.targetDateIcon.hide();
            this.targetDateControl.hide();
        } else {
            this.targetDateIcon.show();
            this.targetDateControl.show();
        }
    }

    /** change the state of the status */
    private updateStatusState(status: SmartStepStatus, userInteraction: boolean) {
        // status state update
        this.latest.status = status;

        // we can only update an actonDef if we have one (asCreating means one is being created)
        // we should only trigger this event if its a user interaction, not the system priming initial data
        // because we don't want to trigger HACT questions on existing data
        if (userInteraction && !this.initialData.asCreatingFromEmpty) {
            events.GoalTransientStatusEvent.bus.fire(
                new events.GoalTransientStatusEvent(this.getActionDef().getId(), status));

            // use initial text if we can
            const initialText = this.sessionData.getAnyActionById(this.actionDef.getId()).getInitialText();
            if (initialText != null && initialText != "" && !this.latest.intervention) {
                this.latest.intervention = initialText;
                this.refreshIntervention();
            }
        }
    }

    /** sets a target on a smart step */
    public updateExpiryState(newDate: string) {
        // ensure consistency between input of 'empty' to our null/undefined internal expectations
        this.latest.expiryDate = newDate == "" ? null : newDate;
    }

    /** sets a target on a smart step */
    public updateTargetState(newDate: string) {
        // ensure consistency between input of 'empty' to our null/undefined internal expectations
        this.latest.targetDateTime = EccoDate.parseIso8601(newDate)?.toDateTimeMidnight()?.formatIso8601() || null;
        this.instanceState.targetDate = newDate == "" ? null : newDate;
    }

    // NB this is to maintain state, but really we also have the same in this.scheduleControl.getSchedule()
    public updateTargetScheduleState(newSchedule: string) {
        // ensure consistenct between input of 'empty' to our null/undefined internal expectations
        this.latest.targetSchedule = newSchedule == "" ? null : newSchedule;
        this.instanceState.targetSchedule = this.latest.targetSchedule;
    }

    /** sets a goal on an individual smart step */
    public updateGoalNameState(newName: string) {

        // ensure consistenct between input of 'empty' to our null/undefined internal expectations
        if (newName == "") {
            newName = null;
        }

        // comment state update (ui already updated)
        this.latest.goalName = newName;
    }

    /** sets a goal plan/comment on an individual smart step */
    public updateGoalPlanState(newComment: string) {

        // ensure consistenct between input of 'empty' to our null/undefined internal expectations
        if (newComment == "") {
            newComment = null;
        }

        // comment state update (ui already updated)
        this.latest.goalPlan = newComment;
    }

    /** sets a score on an individual smart step */
    public updateScoreState(newScore: number) {
        this.latest.score = newScore;
    }

    /** sets the hierarchy of an individual smart step */
    public updateHierarchyState(hierarchy: number) {
        this.latest.hierarchy = hierarchy;
    }

    /** sets the orderby of an individual smart step */
    public updatePositionState(position: string) {
        this.latest.position = position;
    }

    /** Add an activity change against the appropriate/original referral - parent possibly */
    private updateActivityState(idSelectedStrArr: number[]) {
        if (idSelectedStrArr) {
            this.currentActivityState = idSelectedStrArr.map(str => str);
        }
    }

    /** sets a changeReason on a smart step */
    public updateChangeReasonState(reasonId: number) {
        // ensure consistenct between input of 'empty' to our null/undefined internal expectations
        this.latest.statusChangeReasonId = reasonId;
    }

    /** True if required fields are set */
    public isValid(): boolean {
        let valid = true;
        const outcomeId = this.actionDef.getOutcome().getId();
        if (this.controlFeatures.validateActionComponents(outcomeId, 'score') && this.scoreListGroup) {
            const forced = this.controlFeatures.forceActionComponents(outcomeId, 'score');
            const allowedToBeEmpty = !forced && !this.isRelevant();
            if (!allowedToBeEmpty && !this.scoreListGroup.isValid()) {
                valid = false;
            }
        }
        if (this.controlFeatures.validateActionComponents(outcomeId, 'targetDate')) {
            const forced = this.controlFeatures.forceActionComponents(outcomeId, 'targetDate');
            const allowedToBeEmpty = !forced && !this.isRelevant();
            if (this.showTargetDateControl() && !this.latest.targetDateTime && !allowedToBeEmpty) {
                valid = false;
            }
        }
        if (this.statusChangeReasonGroup && !this.statusChangeReasonGroup.isValid()) {
            valid = false;
        }
        return valid;
    }

    /**
     * relate the work to this smart step (or 'undo' related)
     * @param directClick differenciates between ensuring the 'relevant' is updated because of other actions
     *      and allowing the user to attempt to toggle it - if allowed
     */
    private updateRelatedState(directClick: boolean) {
        let forceOn = this.isForcedRelatedState(); // indicate that we want to ensure the 'related' chain symbol is on
        let toggle = false;

        // do a toggle if the user is allowed
        if (directClick && !forceOn) {
            toggle = true;
        }

        // do a toggle if we are currently off but meant to be on
        if (forceOn && !this.currentRelated) {
            toggle = true;
        }
        // do a toggle if we were forced but are no longer
        // NB we do in the jsp version remember if the user clicked 'relevant'
        // so that if we un-set the relevant we bring back what the user did
        // but this seems a little overkill for the benefit
        if (!directClick && !forceOn && this.currentRelated) {
            toggle = true;
        }

        if (toggle) {
            this.currentRelated = !this.currentRelated;
        }
    }

    private isForcedRelatedState(): boolean {

        let forceOn = false; // indicate that we want to ensure the 'related' chain symbol is on

        // if we have a different status, we ensure it will be on
        if (this.latest.status != this.initialData.status) {
            forceOn = true;
        }
        // if we have a different date, we ensure it will be on
        const targetDatesDiffer = this.latest.targetDateTime && this.initialData.targetDate
                ? EccoDateTime.parseIso8601(this.latest.targetDateTime).toEccoDate().compare(EccoDate.parseIso8601(this.initialData.targetDate)) != 0
                : (this.latest.targetDateTime != null || this.initialData.targetDate != null) // one is null the other isn't
        if (targetDatesDiffer || (this.latest.targetSchedule != this.initialData.targetSchedule)) {
            forceOn = true;
        }
        // if we have a different comment, we ensure it will be on
        let ignoreStatus = this.context.features.isEnabled("support.evidence.goalPlanIgnoresStatus");
        if (!ignoreStatus && this.latest.goalPlan != this.initialData.goalPlan) {
            forceOn = true;
        }

        return forceOn;
    }

}
export = SupportInstanceControl;
