import $ = require("jquery");
import Lazy = require("lazy");
import MultiInstanceListControl = require("../../evidence/multi-instance/MultiInstanceListControl");
import {EvidenceContext, HierarchyPosition} from "ecco-evidence";
import BaseControl = require("../../controls/BaseControl");
import Sequence = LazyJS.Sequence;
import {MultiActionsControl} from "./MultiActionsControl";
import {CommandQueue} from "ecco-commands";
import {ActionComponent, ActionGroupComponent, EvidenceUpdateEvent, OutcomeComponent, SmartStepStatus} from "ecco-dto";
import {GoalUpdateCommandDto, SupportAction} from "ecco-dto/evidence-dto";
import {
    ActionDefParent,
    ActionGroupDef,
    ActionInstanceControl,
    ActionInstanceControlFactory,
} from "../../evidence/evidenceControls";
import {ActionInstanceParentFeatures} from "ecco-evidence";
import {NumberToObjectMap} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {ActionInstanceControlDataSetup, EvidenceDisplayOptions} from "ecco-evidence";
import {ResizeEvent} from "@eccosolutions/ecco-common";


/**
 * The OutcomeControl delegates to here to handle the creation of the tab's content.
 * This is the same level as OutcomeControl, except specific focus on the smart steps.
 * It creates ActionGroupControl's (in the file below) which each can hold actionDefId's which each can hold many ActionDefParent's.
 * MultiInstanceControl (the parent class) also holds the ActionInstance controls, but doesn't have the concept of the ActionGroups.
 */
class SnapshotBasedMultiInstanceListControl<CHILD_TYPE extends ActionInstanceControl> extends MultiInstanceListControl<CHILD_TYPE> {

    // 'groupControls' where each group can hold many actionDefId
    // groups are in addition to the 'actionsControls' in the parent
    // because we sometimes want behaviours on an a whole actionDefId group (eg panel drop-down/up)
    private groupControls: Array<ActionGroupDef>;
    private groupControlsById: NumberToObjectMap<ActionGroupDef>;

    constructor(context: EvidenceContext, outcomeDef: OutcomeComponent,
                isNeedsBasedOnly, showUnachievedOnly: boolean,
                latestEvidence: SupportAction[],
                childCtor: ActionInstanceControlFactory<CHILD_TYPE>,
                controlsQ: Promise<any>[]) {
        super(context, outcomeDef, isNeedsBasedOnly, showUnachievedOnly, latestEvidence, childCtor, controlsQ);
    }

    public render() {
        this.groupControls.forEach(groupControl => {
            this.element().append(groupControl.element());
            groupControl.render();
        });
        if (this.outcomeDef.getActionGroups().length > 0 && this.isEmpty()) {
            this.element().append('<p class="text-center text-muted">nothing selected for this area</p>');
        }
    }

    public changedDisplayOptions(filter: EvidenceDisplayOptions) {
        this.groupControls.forEach(c => c.changedDisplayOptions(filter));
    }

    /** True if required fields are set */
    public isValid(): boolean {
        // return false early through every
        return this.groupControls.every(group => group.isValid());
    }

    public isEmpty() {
        // we have group controls but those should be empty to count as empty
        return this.groupControls.reduce((acc, control) => acc + control.count(), 0) == 0;
    }

    /** Mark as being relevant if this control has any actions marked as relevant from default - i.e. */
    public isRelevant() {
        return this.groupControls.reduce((acc, control) =>
            acc + control.relevantCount(), 0) > 0;
    }

    public emitChangesTo(queue: CommandQueue) {
        this.groupControls.forEach(control => control.emitChangesTo(queue));
    }

    protected initialisePrivateVars() {
        this.groupControls = [];
        this.groupControlsById = {};
    }

    // render from the data or render 'nothing selected for this area'
    protected initialiseFromDataOrMsg($messageContainer: $.JQuery) {
        this.outcomeDef.getActionGroups().forEach(groupDef => {
            const grpEnabled = !groupDef.isDisabled() &&
                                Lazy(groupDef.getActions()).some(def => !def.isDisabled());
            if (grpEnabled) {
                this.createActionGroup(groupDef, this.latestEvidence);
            }
        });
    }

    /**
     * Return a created MultiActionsControl for a new command event (eg add goal).
     * A mac is created for each new smart step/goal, because each one can become a parent itself.
     */
    protected createMultiActionsControlFromCommand(event: EvidenceUpdateEvent,
                                                   rootUuid: string): MultiActionsControl<CHILD_TYPE> {

        const goalDto = event.commandDto as GoalUpdateCommandDto;
        const controlUuid = Uuid.randomV4();
        const hierarchy = goalDto.hierarchyChange && goalDto.hierarchyChange.to;
        const position = new HierarchyPosition(goalDto.positionChange && goalDto.positionChange.to);
        const parentFeatures = this.getParentControlFeatures(hierarchy);
        const actionDef: ActionComponent = this.context.configResolver.getServiceType().getAnyActionById(event.commandDto.actionDefId);
        return new MultiActionsControl<CHILD_TYPE>(this.context.features,
            this.context.serviceRecipientId, this.context.evidenceDef, this.context, actionDef, parentFeatures, this.childCtor,
            (parentInstanceUuid: string, hierarchy: number) => this.childControlsOf(parentInstanceUuid, hierarchy), position, hierarchy, controlUuid.toString(), rootUuid);
    }

    protected createMultiActionsControlFromSnapshot(parentControlFeatures: ActionInstanceParentFeatures,
                                                    actionDef: ActionComponent, controlUuid: Uuid, sa: SupportAction | null,
                                                    actionGroup: ActionGroupDef): MultiActionsControl<CHILD_TYPE> {
        const hierarchy = (sa && sa.hierarchy) || 0;
        const position = new HierarchyPosition(sa ? sa.position : "0");
        const rootUuid = sa
            ? hierarchy == 0 ? sa.actionInstanceUuid : sa.parentActionInstanceUuid
            : controlUuid.toString();
        return new MultiActionsControl<CHILD_TYPE>(this.context.features,
            this.context.serviceRecipientId, this.context.evidenceDef, this.context, actionDef, parentControlFeatures, this.childCtor,
            (parentInstanceUuid: string, hierarchy: number) => this.childControlsOf(parentInstanceUuid, hierarchy), position, hierarchy, controlUuid.toString(), rootUuid);
    }

    /**
     * Handles any command update for the provided mac (ActionDefParent) - whether the mac or command is new or not.
     * NB For snapshots-based controls, this method is only triggered on new command events (eg add goal)
     */
    protected appendControl(mac: MultiActionsControl<CHILD_TYPE>,
                            actionDefId?: number,
                            hierarchy?: number, position?: HierarchyPosition) {

        // for actionGroups with showAs goals - ensure the action group exists
        // When EvidenceUpdateEvent is fired the flow from MultiInstanceListControl is to create a MultiInstanceListControl if we don't have one for the parent.
        // MILC is used by this class through addControlAndAppend in createParentGroups below, so the private vars in the super MILC are populated.
        // So we replace the previous 'addActionFromAddGoal' (which came from GoalEvidenceEvent) with this method which comes from EvidenceUpdateEvent.
        // In doing so, we find the flow from MILC covers quite a lot - except ensuring our actionGroup exists, so we use getOrCreateActionGroupControlFor
        // (but we use this method in the applyMultiActionsControlFromCommand so we can also render it)

        const actionDef: ActionComponent = this.context.configResolver.getServiceType().getAnyActionById(actionDefId);

        // TODO needs testing for the drop down group behaviour
        let actionGroupControl = this.getOrCreateActionGroupControlFor(actionDef.getActionGroupComponent());

        // now ensure the container is there for the 'applyCommand'
        actionGroupControl.ensureControlInPlace(mac, position);
    }

    /**
     * Get features for ActionDefParent's (MultiActionsControl) for snapshot-based evidence
     */
    private getParentControlFeatures(hierarchy: number): ActionInstanceParentFeatures {
        switch (hierarchy) {
            case 0:
                break; // skip to below
            case 1:
                return new ActionInstanceParentFeatures()
                    .setShowNewActions(true)
                    .setShowSubsteps(false);
            case 2:
                return new ActionInstanceParentFeatures()
                    .setShowNewActions(true)
                    .setShowSubsteps(false);
            default:
                throw new Error("unknown hierarchy");
        }

        let hierarchy0Features = new ActionInstanceParentFeatures()
            .setNeedsBased(this.isNeedsBasedOnly)
            .setShowUnachievedOnly(this.showUnachievedOnly)
            .setShowAsPlaceholder(this.isNeedsBasedOnly);

        let subSteps = this.context.configResolver.getServiceType().taskDefinitionSettingHasFlag(
            this.context.evidenceDef.getTaskName(),  "showSubActions", "y");
        let newActions = this.context.configResolver.getServiceType().taskDefinitionSettingHasFlag(
            this.context.evidenceDef.getTaskName(),  "showNewActions", "y");

        hierarchy0Features
            .setShowNewActions(newActions)
            .setShowSubsteps(subSteps)
            .getActionDefFeatures(this.context, 0);

        return hierarchy0Features;
    }

    /** Get, creating if necessary the group for several actionDefIds */
    private getOrCreateActionGroupControlFor(group: ActionGroupComponent) {
        if (!this.groupControlsById[group.getId()]) {
            let control = new ActionGroupControl(this.context, group);
            this.groupControlsById[group.getId()] = control;
            this.groupControls.push(control);
        }
        return this.groupControlsById[group.getId()];
    }

    /** Main entry method to populate each actionGroup of the outcome tab */
    private createActionGroup(groupDef: ActionGroupComponent, supportActions: SupportAction[]): ActionGroupDef {
        let actionGroup = this.getOrCreateActionGroupControlFor(groupDef); // ensures empty groups created in order even if empty
        // create multi action groups
        this.createParentGroups(groupDef, supportActions, actionGroup);
        return actionGroup;
    }

    /**
     * Main entry method to determine which data to show.
     * For each actions within the actionGroup we find the supportActions and create MultiActionsControl using the parent's 'addControlAndAppend'
     * which keeps the data structures in tact.
     */
    private createParentGroups(groupDef: ActionGroupComponent, supportActions: SupportAction[], actionGroup: ActionGroupDef) {
        groupDef.getActions()
            .filter(actionDef => !actionDef.isDisabled())
            .map(actionDef => {
            // within each actionId sort by database identifier order (meaning order added)
            // BUT also ensure unachieved to the top
            const supportActionsMatchesForActionDefId = supportActions
                .filter(supportAction => supportAction.actionId == actionDef.getId())
                .sort((sa1, sa2) => {
                    const sa1Multiple = sa1.status == SmartStepStatus.Achieved ? 1 : 100;
                    const sa2Multiple = sa2.status == SmartStepStatus.Achieved ? 1 : 100;
                    const value = (sa1Multiple * sa1.id) - (sa2Multiple * sa2.id);
                    return value;
                });

            // if hierarchical supportActions are parked and have no data, then don't include - even on an assessment page
            // if hierarchical supportActions are commentOnly, we keep (so no code change)
            const supportActionsToHide = supportActionsMatchesForActionDefId
                    .filter(sa => sa.hierarchy > 0)
                    .filter(sa => sa.status == SmartStepStatus.NoLongerWanted)
                    .filter(sa => ActionInstanceControlDataSetup.isEmpty(sa))
                    .map(sa => sa.actionInstanceUuid);

            const supportActionsRelevantForActionDefId = supportActionsMatchesForActionDefId
                    .filter(sa => supportActionsToHide.indexOf(sa.actionInstanceUuid) == -1)

            if (supportActionsRelevantForActionDefId.length > 0) {
                this.createParentGroupFromData(actionDef, supportActionsRelevantForActionDefId, actionGroup);

            } else if (this.getParentControlFeatures(0).isShowAsPlaceholder()) {
                const controlUuid = Uuid.randomV4();
                const data = null;
                this.setupFromSnapshot(this.getParentControlFeatures(0), actionDef, controlUuid, data, actionGroup);
                this.controlsQ.push(Promise.resolve(controlUuid));
            }

        });
    }

    /** Return the depth we are to the top from here - ie 0 means we are already at the top */
    private createParentGroupFromData(actionDef: ActionComponent, data: SupportAction[], actionGroup: ActionGroupDef) {
        // for sub-smartsteps, find the root levels only and sort them by their order
        const saRootsOrdered = Lazy(data)
            .filter(saAll => saAll.hierarchy != null ? saAll.hierarchy == 0 : true) // root level is 0 or not defined
            .sortBy(saRoot => {
                const position = new HierarchyPosition(saRoot.position);
                return position.orderby();
            });

        // for each root, create all its children
        saRootsOrdered.each(saRoot => this.createParentGroupFromDataRecursive(saRoot, actionDef, Lazy(data), actionGroup));
    }

    private createParentGroupFromDataRecursive(sa: SupportAction, actionDef: ActionComponent, data: Sequence<SupportAction>, actionGroup: ActionGroupDef) {
        const currentRoot = sa.parentActionInstanceUuid || sa.actionInstanceUuid;
        const currentHierarchy = sa.hierarchy != null ? sa.hierarchy : 0;

        // see 9943763b
        this.setupFromSnapshot(this.getParentControlFeatures(currentHierarchy), actionDef, Uuid.parse(sa.actionInstanceUuid), sa, actionGroup);

        const immediateChildren = data
        // children have to have a root, hierarchy and orderby
            .filter(item => (item.hierarchy != null && item.position != null))
            // the children need to have the root
            .filter(child => child.parentActionInstanceUuid == currentRoot)
            // get the children
            .filter(saRoot => (saRoot.hierarchy == currentHierarchy + 1))
            .sortBy(saChild => {
                const position = new HierarchyPosition(saChild.position);
                return position.orderby();
            }); // sort order
        if (immediateChildren.size() > 0) {
            immediateChildren.each(saChild => this.createParentGroupFromDataRecursive(saChild, actionDef, data, actionGroup));
        }
    }

}

function checkIfInView(element: $.JQuery): boolean {
    var offset = element.offset().top - $(window).scrollTop();

    if (offset > window.innerHeight) {
        // Not in view so scroll to it
        $('html,body').animate({scrollTop: offset}, 1000);
        return false;
    }
    return true;
}

/**
 * Handle the creation of each actionGroup - which could be many ActionDefParent's, each with multiple smart steps
 */
class ActionGroupControl extends BaseControl implements ActionGroupDef {

    // multiple parents are possible (MultiActionsControl) within this group
    private parentControls: Array<ActionDefParent> = [];
    private $actions = $("<div>").addClass("action-group-div");
    private hasActionGroups: boolean;

    constructor(private context: EvidenceContext,
                private groupDef: ActionGroupComponent) {
        super();

        this.hasActionGroups = groupDef.getOutcome().getActionGroups().length > 1;

        const $title = $("<h3>").addClass("action-group button clickable")
            .text(groupDef.getName())
            .click(() => this.toggleCollapse());

        if (this.hasActionGroups) {
            this.element().append($title);
        }
        this.element().append(this.$actions);
    }

    public render() {

        if (this.parentControls.length > 0) {

            let relevant = false;
            // render all the parents first because we want to embed some of the children in existing parentControls
            this.parentControls.forEach(control => {
                // the order of the parentControls are already provided by createMultiActionsControlFromSnapshot
                // and we are rendering in order, so we simply render all the hierarchies in the order provided
                // see findMultiActionsControlPosition for when rendering from commands (not snapshots)
                relevant = relevant || control.isRelevant();
            });

            // hide any where nothing relevant
            if (!relevant && this.hasActionGroups) {
                this.toggleCollapse(true);
            }

        }
    }

    /**
     * Dynamically add a new smart step container, if needed.
     * Potentially we need to add the ActionDefParent (MultiActionsControl) to the dom to render as a container to the command
     * NB This method does nothing with the command itself, only the container:
     *      if the command doesn't have a parent, its a new hierarchy 0, so does a sneaky add/render directly to actionDefPosition
     *      if the command has a parent, then find it - if it doesn't exist then we add the mac as a new mac child of the parent
     */
    public ensureControlInPlace(ctl: ActionDefParent, position: HierarchyPosition) {

        if (!ctl.isAppliedToDom()) {
            // find the right place - this is either a root node, or an inner element
            const $actionDefPosition = this.findElementForControl(ctl.getRootUuid(), ctl.getActionDefId(), position);
            $actionDefPosition.append(ctl.element());
            // add afterwards, so there is no potential for it to find itself as the potential position
            this.parentControls.push(ctl);
        }

        // TODO if showAs goals, check if we need a new toggle group

        // NB this resize now focuses on the multiple control, not the individual ActionInstanceControl - which might be an issue
        ResizeEvent.bus.fire(new ResizeEvent());
        // Ensure the specific smart step is also visible
        checkIfInView(ctl.element());
    }

    public hasActionDefId(actionDefId: number) {
        return this.parentControls.some(p => p.getActionDefId() == actionDefId);
    }

    public changedDisplayOptions(displayOptions: EvidenceDisplayOptions) {
        this.parentControls.reduce((prev: boolean, ctl: ActionDefParent) => ctl.changedDisplayOptions(displayOptions) && prev, true);
        // parent controls are the whole outcome, so we can't meaningfully hide this
    }

    public emitChangesTo(queue: CommandQueue) {
        this.parentControls.forEach(control => control.emitChangesTo(queue));
    }

    public isValid(): boolean {
        return this.parentControls.every(ctl => ctl.isValid());
    }

    public count(): number {
        return this.parentControls.length;
    }

    public relevantCount(): number {
        return this.parentControls.reduce((prev, ctl) => prev + (ctl.isRelevant() ? 1 : 0), 0);
    }

    private toggleCollapse(forceCollapse?: boolean) {
        if (forceCollapse) {
            this.parentControls.forEach(control => control.element().hide());
        } else {
            this.parentControls.forEach(control => control.element().slideToggle());
        }
    }

    /**
     * Find the control's dom placement for a given position.
     */
    private findElementForControl(rootUuid: string,
                                  actionDefId: number,
                                  position: HierarchyPosition): $.JQuery {

        const previousControl = position.previous() == null
                ? null
                // find the previous - either a parent or a sibling
                // but possibly not available due to deleting, for instance
                : this.getControlFromExistingControls(rootUuid, actionDefId, position.previous());

        // if we have no previous, return null - we are 'it', so attach ourselves to the action group directly
        if (!previousControl) {
            // check if we need a new actionDef group first
            const hasActionDefCtls = this.parentControls.filter(p => p.getActionDefId() == actionDefId);
            if (hasActionDefCtls.length == 0) {
                const $actionDef = $("<div>").addClass("action-def-id-" + actionDefId);
                this.$actions.append($actionDef);
                return $actionDef;
            } else {
                return this.$actions.find(".action-def-id-"+actionDefId);
            }
        }

        // if the new control is the first time sub-smart step (position is 'x-0') then find the embedded bit
        if (position.hierarchy() > 0 && position.orderby() == 0) {
            return previousControl.element().find(".multi-action-controls-hierarchical").first();
        } else {
            // if we are an action def match, we want to return the parent so that the new action isn't inside the others
            if (position.hierarchy() == 0) {
                return previousControl.element().closest(".action-def-id-"+actionDefId);
            }
        }

        // if we are a sub-smart step, not the first
        return previousControl.element().closest(".multi-action-controls-hierarchical");
    }

    private getControlFromExistingControls(rootUuid: string, actionDefId: number, position: HierarchyPosition): ActionDefParent {

        // try to find the parent of the same root node
        const rootCtl = this.parentControls
            .filter(ctl => ctl.getRootUuid() == rootUuid) // will already be the same actionDefId
            // NB matching exactly should work, but some dodgy data exists (ie goals all have the same position -0 due to 3d9602b7)
            // so we match as close as we can
            //.filter(ctl => ctl.getPosition() == position.asString())
            .filter(ctl => ctl.getHierarchy() == position.hierarchy()) // always match the hierarchy
            .filter(ctl => new HierarchyPosition(ctl.getPosition()).orderby() <= position.orderby()) // find good enough matches
            .sort((a,b) => {
                let aPos = new HierarchyPosition(a.getPosition());
                let bPos = new HierarchyPosition(b.getPosition());
                return (aPos.orderby() > bPos.orderby()) ? 0 : 1;
            })
            .pop();

        // otherwise find the placeholder for the matching action def
        const actionDefCtl = this.parentControls
            .filter(ctl => ctl.getActionDefId() == actionDefId)
            // we only want to find top level actionDefIds (ie not sub ones)
            .filter(ctl => ctl.getHierarchy() == 0)
            // get the last one
            .sort((a,b) => {
                let aPos = new HierarchyPosition(a.getPosition());
                let bPos = new HierarchyPosition(b.getPosition());
                return (aPos.orderby() > bPos.orderby()) ? 0 : 1;
            })
            .pop();

        return rootCtl || actionDefCtl;
    }

}

export = SnapshotBasedMultiInstanceListControl;
