import $ = require("jquery");
import BaseControl = require("../../controls/BaseControl");
import * as dto from "ecco-dto/evidence-dto";
import {ActionInstanceControlDataSetup, EvidenceDef, EvidenceDisplayOptions, HierarchyPosition} from "ecco-evidence";
import {SessionData} from "ecco-dto";
import evidenceControls = require("../../evidence/evidenceControls");
import events = require("../events");
import {CommandQueue} from "ecco-commands";
import {ActionInstanceControlFactory} from "../../evidence/evidenceControls";
import {ActionInstanceControlData, EvidenceContext, ActionInstanceParentFeatures} from "ecco-evidence";
import {EvidenceGroup, SmartStepStatus} from "ecco-dto";
import {SupportAction} from "ecco-dto/evidence-dto";
import {ActionComponent} from "ecco-dto";
import {EvidenceUpdateEvent} from "ecco-dto";
import {GoalUpdateEvent} from "../events";
import {GoalUpdateCommand} from "ecco-commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import BaseAsyncDataControl = require("../../controls/BaseAsyncDataControl");
import SupportHistoryListControl = require("../../evidence/support/SupportHistoryListControl");
import {showInModalDom} from "ecco-components";
import Panel = require("../../controls/Panel");
import RiskHistoryListControl = require("../risk/RiskHistoryListControl");

/**
 * A container for embedding multiple controls (ie multiple smart steps) identified by an parentUuid.
 * NB ironically 'children' of this control is only ever one element (due to development over time)
 * Therefore this is a container for one smart step. SnapshotBasedMultiInstanceListControl handles the groupings,
 * and the position determines the display (eg if there is a panel)
 */
export class MultiActionsControl<CHILD_TYPE extends evidenceControls.ActionInstanceControl> extends BaseControl implements evidenceControls.ActionDefParent {

    private $panel: Panel;
    private $wrapper: $.JQuery;
    private $inner = $("<div>");
    private appliedToDom = false;
    private $menu: $.JQuery;

    private hierarchy = 0;
    private position = new HierarchyPosition("0");
    // NB ironically 'children' of this control is only ever one element
    private children: CHILD_TYPE[] = [];
    private childrenById: { [id: string]: CHILD_TYPE } = {}; // id == actionInstanceUuid
    public readonly uniqueContextUuid = Uuid.randomV4().toString();

    /**
     * NB rootUuid (used for parentInstanceUuid) and position are the elements that determines the position of the smart step.
     * @param controlFeatures features to show for an instance
     * @param childCtor factory to construct an instance
     * @param position exact position 0-based, eg 0-1-0 means the 'root's second child's first child' (see HierarchyPosition)
     * @param hierarchy depth (0-based) is superfluous with position, but intended for reporting, eg 0-1-0 position would be hierarchy 2 (3rd)
     * @param controlUuid the uuid of this control, which is typically the smart step actionInstanceUuid
     * @param rootUuid the root node actionInstanceUuid at the very top of the hierarchy to apply to all its smart steps parentActionInstanceUuid
     */
    constructor(private sessionData: SessionData, private serviceRecipientId: number, private evidenceDef: EvidenceDef,
                private context: EvidenceContext, private actionDef: ActionComponent,
                private controlFeatures: ActionInstanceParentFeatures,
                private childCtor: ActionInstanceControlFactory<CHILD_TYPE>,
                private childControlsOf?: (parentInstanceUuid: string, hierarchy: number) => number,
                position?: HierarchyPosition, hierarchy?: number, private controlUuid?: string,
                private rootUuid?: string) {
        super($("<div>"));

        this.subscribeToEvents();

        if (hierarchy > 0) {
            this.hierarchy = hierarchy;
        }
        if (position != null && position.asString() != null) {
            this.position = position;
        }

        if (rootUuid) {
            const id = "mac-" + rootUuid + "-" + this.position.asString();
            this.element().attr("id", id);
        }

        this.$wrapper = $("<div>")
            .addClass("multi-action-control");
            //.append($("<span>").text(rootUuid));
        this.$wrapper.append(this.$inner);

        // if we are the top hierarchy
        if (this.hierarchy == 0) {
            this.$panel = new Panel();
            this.$panel.bodyElement().append(this.$wrapper);
            this.element().append(this.$panel.element());
        } else {
            this.element().append(this.$wrapper);
        }

        this.createMenu();
    }

    public render() {

        //this.$inner.empty();

        if (this.controlFeatures.isShowTitle()) {
            if (this.actionDef) {
                this.$wrapper.append($("<h4>").text(this.actionDef.getName()));
            }
        }

        this.children.forEach(childCtl => {
            this.$wrapper.append(childCtl.element());
            childCtl.render();
        });

        // if we have more than one then we were created with multiple smart steps
        // NB note that we might not have 'showNewActions' configured on this page
        // but we still want the hiding behaviour.
        if (this.children.length > 1) {
            // for any achieved, hide the details
            this.children.forEach(actionDefCtl => {
                if (actionDefCtl.isAchieved()) {
                    actionDefCtl.showSummaryOnly();
                }
            });
        }

        if (this.$menu) {
            const $footer = $("<div>").addClass("col-xs-12");
            $footer.append(this.$menu);
            this.$wrapper.append($("<div>").addClass("row").append($footer));
        }

        this.$wrapper.append($("<div>").addClass("multi-action-controls-hierarchical"));

        this.setAppliedToDom(true);
    }

    protected getHistoryControl(): BaseAsyncDataControl<any> {
        // History button when there is some history on that risk/needs smart step
        let control: BaseAsyncDataControl<any>;
        if (this.hasHistory()) {
            if (this.context.evidenceDef.getEvidenceGroup() == EvidenceGroup.threat) {
                control = new RiskHistoryListControl(this.context.serviceRecipientId, null, this.actionDef.getId());
            } else {
                control = new SupportHistoryListControl(this.context.serviceRecipientId,
                    this.context.evidenceDef.getTaskName(), this.actionDef.getId());
            }
        }
        return control;
    }

    public changedDisplayOptions(displayOptions: EvidenceDisplayOptions) {
        // NB children is always one! see class notes
        const hasAllFiltered = this.children.reduce((prev: boolean, ctl: CHILD_TYPE) => ctl.changedDisplayEvidence(displayOptions) && prev, true);
        // if we achieve the top smart step, all of them get hidden
        if (this.$panel) {
            this.$panel.element().toggle(!hasAllFiltered);
        } else {
            this.$wrapper.toggle(!hasAllFiltered);
        }
        return hasAllFiltered;
    }

    public isValid(): boolean {
        return this.children.reduce( (prev, ctl) => ctl.isValid() ? prev : false, true );
    }
    public count(): number {
        return this.children.length;
    }
    public isRelevant(): boolean {
        return this.children.reduce( (prev, ctl) => ctl.isRelevant() ? prev : false, true );
    }
    public hasHistory(): boolean {
        return this.children.reduce( (prev, ctl) => ctl.hasHistory() ? prev : false, true );
    }

    /** Not unique across parents */
    public getActionDefId() {
        return this.actionDef.getId();
    }
    /** Unique across parents */
    public getControlUuid() {
        return this.controlUuid;
    }
    public getRootUuid() {
        return this.rootUuid;
    }
    /** 0 is root (tld), 1.. is level under */
    public getHierarchy() {
        return this.hierarchy;
    }
    public getPosition() {
        return this.position.asString();
    }

    public emitChangesTo(queue: CommandQueue) {
        this.children.forEach( control => control.emitChangesTo(queue) );
    }

    /** INCOMING DATA **/
    /** Apply the command from MultiInstanceListControl's EvidenceUpdateEvent handler (but not all implement applyCommand)
     * MyPlan - apply the command from 'my plan' event bus - we translate into getOrCreateControlFromCommand
     */
    public applyCommand(cmd: dto.BaseServiceRecipientCommandDto) {
        let commandDto = <dto.GoalUpdateCommandDto>cmd;
        const ctl: CHILD_TYPE = this.getOrCreateControlFromCommand(commandDto);
        ctl.applyCommand(commandDto);
        this.ensureMenuShowing();
    }
    /** Apply the loaded data */
    public applySnapshot(supportAction: SupportAction) {
        const data = ActionInstanceControlDataSetup.fromSnapshot(supportAction, false, []);
        const ctl: CHILD_TYPE = this.createControlFromSnapshot(data);
        ctl.applySnapshot(data);
        if (supportAction) {
            this.ensureMenuShowing();
        }
    }
    /** INCOMING DATA **/

    public isAppliedToDom(): boolean {
        return this.appliedToDom;
    }
    public setAppliedToDom(value: boolean) {
        this.appliedToDom = value;
    }

    public static shouldHideWhenGoalPlanWithNotRelevant(context: EvidenceContext, supportAction: SupportAction): boolean {
        // HACK
        // prevent showing the legacy 'feature' on the support plan - keep all other logic
        // this only applies to the one service where we have showComment enabled on tabular smart step control
        let ignoreStatus = context.features.isEnabled("support.evidence.goalPlanIgnoresStatus");
        const showGoalPlan = context.configResolver.getServiceType().taskDefinitionSettingHasFlag(context.evidenceDef.getTaskName(), "showActionComponents", "comment");
        if (ignoreStatus && showGoalPlan) {
            // prevent rendering the action if status is 2 (see emitChangesTo)
            // and we are on a reduction only page
            let hasSomeAssessment = context.configResolver.getServiceType().taskDefinitionSettingHasFlag(context.evidenceDef.getTaskName(),  "actAs", "assessment");
            // ignore the CommentOnly - this shouldHideWhenGoalPlanWithNotRelevant should be a thing of the past
            // see also support.evidence.goalPlanIgnoresStatus in SmartStepRoot.tsx
            if (supportAction && (supportAction.status == SmartStepStatus.NoLongerWanted && !hasSomeAssessment)) {
                // hide
                return true;
            }
        }
        return false;
    }

    // NB we use an event because 'isRelevant' 'isAchieved' are not triggers
    private subscribeToEvents() {
        events.GoalTouchedEvent.bus.addHandler((event: events.GoalTouchedEvent) => {
            if (this.controlUuid == event.actionInstanceUuid) {
                this.ensureMenuShowing();
            }
        });
    }
    private ensureMenuShowing() {
        if (this.$menu) {
            this.$menu.show();
        }
    }

    // NB see also AddGoalForm
    // This triggers an event - its up to the handler to do state/ui - see MultiInstanceListControl.subscribe
    private createNew(incomingHierarchy: number) {
        const newCommandUuid = Uuid.randomV4();
        const newActionInstanceUuid = Uuid.randomV4();
        const newHierarchy = incomingHierarchy > this.hierarchy; // either we are 'new action' or 'new sub action'
        // record the new sub action as being after the last child, if there are children (because now our 'new sub action' menu is only available the parent)
        // NB ironically 'children' of this control is only ever one element, so we need to get the children from our parent
        // NB we also use a count, not the last - just because the 'position' of current data is not reliable before this fix
        const lastChildPosition = this.childControlsOf(this.getRootUuid(), incomingHierarchy);
        const newPosition: string = lastChildPosition > 0
            ? this.position.next(newHierarchy).withOrderby(lastChildPosition).asString()
            : this.position.next(newHierarchy).asString();
        const rootHierarchy = incomingHierarchy == 0 ? newActionInstanceUuid : Uuid.parse(this.rootUuid);
        const addGoalCmd = new GoalUpdateCommand("add-instance", newCommandUuid, this.context.getWorkUuid(), this.context.serviceRecipientId,
                this.evidenceDef.getTaskName(), this.actionDef.getId(), this.evidenceDef.getEvidenceGroup(),
                newActionInstanceUuid, rootHierarchy)
                .changeStatus(null, SmartStepStatus.WantToAchieve)
                .changeHierarchy(null, incomingHierarchy)
                .changePosition(null, newPosition);
        const dto: any = addGoalCmd.toCommandDto();
        console.info("InstanceControl cmd created: %s", JSON.stringify(dto));
        GoalUpdateEvent.bus.fire(new GoalUpdateEvent(addGoalCmd, dto));
        // Things like MultiInstanceListControl listen here
        EvidenceUpdateEvent.bus(addGoalCmd.getEvidenceGroupName()).fire(new EvidenceUpdateEvent(dto, this.uniqueContextUuid));
    }

    private createMenu() {

        // show the menu if we are root (so we can see 'history') or the hierarchy is set for sub-smart steps
        // because the 'new action' is catered for in the hierarchy above
        const canCreateMenuByFeatures = this.getHierarchy() == 0 || (this.controlFeatures.isShowSubsteps());

        if (canCreateMenuByFeatures) {
            let $menuContent = $("<ul>")
                .addClass("dropdown-menu dropdown-menu-left")
                .attr("role", "menu");
            // .append($("<li>")
            //     .addClass("dropdown-header")
            //     .attr("role", "presentation")
            //     .text("some title in the drop down"))

            this.$menu = $("<span>")
                .addClass("multi-action-controls-menu dropdown")
                .append($("<button>") // 'a' gives a blue link
                    .addClass("btn btn-default dropdown-toggle")
                    .attr("type", "button")
                    .attr("data-toggle", "dropdown")
                    .append($("<i>").addClass("fa fa-ellipsis-v")))
                // .append($("<span>")
                //     .text("admin")
                //     .append($("<span>").addClass("caret")))) // fa fa-caret-down
                .append($menuContent);

            if (this.controlFeatures.isShowNewActions()) {
                $menuContent.append(this.createNewActionButton());
            }

            if (this.controlFeatures.isShowSubsteps()) {
                $menuContent.append(this.createNewSubActionButton());
            }

            $menuContent.append(this.createHistoryButton());

            // ?
            const canShowMenuByData = this.children.length > 1;
            if (!canShowMenuByData) {
                this.$menu.hide();
            }
        }
    }

    /** Get, creating if necessary, but not duplicating */
    private getOrCreateControlFromCommand(cmd: dto.GoalUpdateCommandDto): CHILD_TYPE {
        if (!this.childrenById[cmd.actionInstanceUuid]) {
            // create empty so that initialData is empty, and updateState can change the state
            const initialData = ActionInstanceControlDataSetup.fromEmpty(false, this.context.serviceRecipientActivityInterest);
            initialData.parentActionInstanceUuid = Uuid.parse(cmd.parentActionInstanceUuid);
            initialData.actionInstanceUuid = Uuid.parse(cmd.actionInstanceUuid);
            this.createControlFromSnapshot(initialData);
        }
        return this.childrenById[cmd.actionInstanceUuid];
    }

    /** Creates a snapshot control */
    private createControlFromSnapshot(initialData: ActionInstanceControlData): CHILD_TYPE {
        // the default is this controlUuid - because in the end each instance is itself a potential mac
        let control = new this.childCtor(this.context, this.sessionData, this.serviceRecipientId, this.evidenceDef,
            this.actionDef, initialData, Uuid.parse(this.controlUuid),
            this.controlFeatures.getActionDefFeatures(this.context, this.hierarchy));
        this.childrenById[control.actionInstanceUuidOrControlUuid()] = control;
        this.children.push(control);
        control.init();
        return control;
    }

    private createNewActionButton(): $.JQuery {
        const messages = this.sessionData.getMessages();
        const supportOrThreat = this.context.evidenceDef.getEvidenceGroup() == EvidenceGroup.threat ? "threat" : "support";
        const newActionText = messages[`${supportOrThreat}.goal.new.label`];
        let $item = $("<li>")
            .append($("<a>")
                .attr("role", "button")
                .addClass("menu-action-edit")
                .append($("<span>").addClass("fa fa-plus"))
                .append($("<span>").text(`  ${newActionText}`))
            );
        $item.click(() => this.createNew(this.hierarchy || 0));
        return $item;
    }

    private createNewSubActionButton(): $.JQuery {
        const messages = this.sessionData.getMessages();
        const supportOrThreat = this.context.evidenceDef.getEvidenceGroup() == EvidenceGroup.threat ? "threat" : "support";
        const newSubActionText = messages[`${supportOrThreat}.subGoal.new.label`];
        let $item = $("<li>")
            .append($("<a>")
                .attr("role", "button")
                .addClass("menu-action-edit")
                .append($("<span>").addClass("fa fa-plus"))
                .append($("<span>").text(`  ${newSubActionText}`))
            );
        $item.click(() => this.createNew(this.hierarchy == null ? 1 : this.hierarchy+1));
        return $item;
    }

    private createHistoryButton(): $.JQuery {
        const historyControl = this.getHistoryControl();
        if (!historyControl) {
            return null;
        }

        let $item = $("<li>")
            .append($("<a>")
                .attr("role", "button")
                .addClass("menu-action-edit")
                .append($("<span>").addClass("fa fa-history"))
                .append($("<span>").text("  history"))
            );
        $item.click(() => {
            historyControl.load();
            showInModalDom("history for this smart step", historyControl.element()[0])
        });
        return $item;
    }

}
