import $ = require("jquery");
import Lazy = require("lazy");

import BaseAsyncDataControl = require("../../controls/BaseAsyncDataControl");
import QuestionnaireHistoryListControl = require("../../evidence/questionnaire/QuestionnaireHistoryListControl");
import events = require("../events");
import {QuestionAnswerTransientEvent, SessionData, SessionDataAjaxRepository, SessionDataRepository} from "ecco-dto";
import HactNotificationHandler = require("./HactNotificationHandler");
import hactWizardForm = require("./HactWizardForm");
import {bus, EccoDateTime} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {
    EvidenceGroup,
    HactSessionData,
    QuestionnaireWorkAjaxRepository,
    QuestionnaireWorkDto,
    ReferralAjaxRepository,
    SmartStepStatus,
    SupportWorkAjaxRepository
} from "ecco-dto";
import {SupportWork} from "ecco-dto/evidence-dto";
import {showFormInModalDom} from "../../components/MUIConverterUtils";
import {HactAjaxRepository} from "../../service-config/hact/HactAjaxRepository";
import {QuestionGroupAjaxRepository} from "../../service-config/QuestionGroupAjaxRepository";
import {HactClientCompositeData, HactClientCompositeDataFull} from "./hactClientCompositeData";
import {Uuid} from "@eccosolutions/ecco-crypto";

const hactRepository = new HactAjaxRepository(apiClient);
const questionGroupRepository = new QuestionGroupAjaxRepository(apiClient);
const questionnaireWorkRepository = new QuestionnaireWorkAjaxRepository(apiClient);
const supportWorkRepository = new SupportWorkAjaxRepository(apiClient);
const referralRepository = new ReferralAjaxRepository(apiClient);
const sessionDataRepository: SessionDataRepository = new SessionDataAjaxRepository(apiClient);

export class HactAnswerEvent {
    public static bus = bus<HactAnswerEvent>();
    constructor() {
    }
}

class HandlerHolder {
    private handler: HactNotificationHandler;
    private instance = Uuid.randomV4();
    public getHandler() {
        return this.handler;
    }
    public setHandler(handler: HactNotificationHandler) {
        this.handler = handler;
    }
}

export class HactNotificationControl extends BaseAsyncDataControl<HactClientCompositeData> {

    private handler = new HandlerHolder();
    private sessionData: SessionData;

    public static hactEnabled(prefix: string, serviceId: number, sessionData: SessionData) {
        return prefix == 'r' && sessionData.hasHactForService(serviceId);
    }

    constructor(private clientId: number,
                private serviceRecipientId: number, // because we store against the 'current referral'
                private showAllOutstanding = false) {
        super();
        this.subscribeToEvents();
    }

    protected fetchViewData(): Promise<HactClientCompositeData> {
        const sessionDataQ = sessionDataRepository.getSessionData();
        const clientIdQ = this.clientId
                ? Promise.resolve(this.clientId)
                : referralRepository.findOneReferralByServiceRecipientId(this.serviceRecipientId).then(sr => {
                    this.clientId = sr.clientId;
                });
        return sessionDataQ.then(sd => {
            this.sessionData = sd;
            return clientIdQ.then(() => { return this.fetchSrData() });
        })
    }

    private fetchSrData(): Promise<HactClientCompositeData> {
        const mappingsQ = hactRepository.findAllOutcomeMappings();
        const evidenceSurveysQ = hactRepository.findAllOutcomeEvidenceSurveys();
        const evidenceActivitiesQ = hactRepository.findAllOutcomeEvidenceActivities();
        const socialValueBankQ = hactRepository.findAllSocialValueBank();
        const hactQuestionsQ = questionGroupRepository.findOneQuestionGroupByName(HactSessionData.HACT_QUESTION_GROUP);
        const hactAnswersQ = questionnaireWorkRepository.findQuestionnaireEvidenceByClientIdAndEvidenceGroupKey(this.clientId, EvidenceGroup.hactQuestionnaire);
        const hactTriggersFromActionsQ: Promise<SupportWork[]> =
            referralRepository.findAllReferralWithoutSecuritySummaryByClient(this.clientId)
                    .then(referrals => {
                        const srIds = referrals.map(r => r.serviceRecipientId);
                        return supportWorkRepository.findAllSupportWorkByStatusChangesByServiceRecipientIds(srIds, EvidenceGroup.needs, true)
                                .then(swArrArr => Lazy(swArrArr)
                                        .map(swForSrId => swForSrId)
                                        .flatten<SupportWork>()
                                        .toArray()
                                );
                    })
        const hactTriggersFromQuestionsQ: Promise<QuestionnaireWorkDto[]> =
                // TODO find the evidencegroup based on questions configured (or configure an array of evidencegroups?)
                //  and possibly limit to the questions configured
                mappingsQ.then(mappings => {
                    const hasQnMappings = mappings.some(m => !!m.questionDefId) // truthy on the values are fine
                    return !hasQnMappings
                            ? Promise.resolve([])
                            : questionnaireWorkRepository.findQuestionnaireWorkByServiceRecipientId(
                            this.serviceRecipientId, EvidenceGroup.fromName("evidencemixed1002"));
                });

        return Promise.all([mappingsQ, evidenceSurveysQ, evidenceActivitiesQ, socialValueBankQ, hactQuestionsQ, hactAnswersQ, hactTriggersFromActionsQ])
            .then( ([mappings, evidenceSurveys, evidenceActivities, socialValueBank, hactQuestions, hactAnswers, actionHistory]) => {
                // NB gave errors when put in Q.all above... indexes not matching weirdness
                return hactTriggersFromQuestionsQ.then(questionHistory => {
                    const hactSessionData = new HactSessionData(mappings, evidenceSurveys, evidenceActivities, socialValueBank, hactQuestions.questions);
                    const data = new HactClientCompositeDataFull(hactSessionData, this.clientId, hactAnswers, actionHistory, questionHistory);
                    return Promise.resolve(data);
                });
            });
    }

    private subscribeToEvents() {
        events.GoalTransientStatusEvent.bus.addHandler(
            (event: events.GoalTransientStatusEvent) => {
                this.triggerMappingAction(event.actionDefId, event.statusTo);
            });
        // NB this will trigger from our own answering of HACT values
        // but we assume that no mappings exist in the 'hact' questionnaire
        QuestionAnswerTransientEvent.bus.addHandler(
            (event: QuestionAnswerTransientEvent) => {
                this.triggerMappingQn(event.questionDefId, event.valueTo);
            });

        HactAnswerEvent.bus.addHandler((event: HactAnswerEvent) => {
            this.load();
        })
    }

    private triggerMappingAction(actionDefId: number, statusTo: SmartStepStatus) {
        if (!this.handler.getHandler()) {
            // NB obviously we won't get an error if the control is loaded too late
            throw new Error("HactNotificationControl: smart step triggered before data initialised");
        }
        if (statusTo == SmartStepStatus.WantToAchieve) {
            this.handler.getHandler().triggerActionDefId(actionDefId);
        }
        // statusTo can be undefined, which is the initial value from the database
        // and indicates that we are resetting the actionDefId to what the page loaded with
        // and hence we want to untrigger
        if ((statusTo == undefined) || (statusTo == SmartStepStatus.NoLongerWanted) || (statusTo == SmartStepStatus.CommentOnly)) {
            this.handler.getHandler().unTriggerActionDefId(actionDefId);
        }
        this.reRender();
    }

    private triggerMappingQn(questionDefId: number, valueTo: string) {
        if (!this.handler.getHandler()) {
            // NB obviously we won't get an error if the control is loaded too late
            throw new Error("HactNotificationControl: answer triggered before data initialised");
        }
        // any value makes it relevant - even if we untick (as it must have been ticked int he first place)
        // however, if we are resetting to the initial value, untrigger
        valueTo != null
            ? this.handler.getHandler().triggerQuestionDefId(questionDefId)
            : this.handler.getHandler().unTriggerQuestionDefId(questionDefId);

        this.reRender();
    }

    protected render(data: HactClientCompositeData): void {
        this.handler.setHandler(new HactNotificationHandler(data, EccoDateTime.nowLocalTime(), this.showAllOutstanding));
        this.reRender();
    }

    private reRender() {
        const questionsOutstanding = this.handler.getHandler().getQuestionsOutstanding();

        const $line1 = $("<p>").text("HACT required on: ");

        if (questionsOutstanding.length == 0) {
            $line1.append("0");
        } else {
            const $questionsOutstanding = $('<a>').css("padding", "0px 5px").text(questionsOutstanding.length);
            $line1.append($questionsOutstanding);
            $questionsOutstanding.click((event: $.JQueryMouseEventObject) => {
                    this.triggerWizard();
                });
        }
        if (this.handler.getHandler().hasHistory()) {
            const $questionsHistory = $('<a>').css("padding", "0px 5px").text("(history)");
            $line1.append($questionsHistory);
            $questionsHistory.click((event: $.JQueryMouseEventObject) => {
                    this.showInModalHistory(
                        this.clientId,
                        this.serviceRecipientId);
                });
        }

        let $line2 = null;
        if (this.handler.getHandler().hasPostSurveysOutstanding() || this.handler.getHandler().hasPreSurveysOutstandingFromHistory()) {

            const daysDue = this.handler.getHandler().getDaysDue();
            const daysExpire = this.handler.getHandler().getDaysExpire();
            const daysDueStr = daysDue == 0 ? "now" : daysDue.toString();
            $line2 = $("<span>").text(
                    "due " + daysDueStr +
                    ", expires in " + daysExpire + " days");
        }

        this.element().empty().append($line1);
        if ($line2) {
            $line1.append($("<br>"));
            $line1.append($line2);
        }
    }

    /**
     * Wizard is triggered through the save action on the evidence screen.
     * Both the submit and cancel events resume the onCompleted method
     * which continues with expected behaviour of submitting the evidence commands
     */
    public checkPromptNeeded(onCompleted: () => void) {
        if (this.handler.getHandler().getPreSurveyQuestionsOutstanding().length > 0) {
            hactWizardForm.showHactInModalByIds(
                this.sessionData,
                this.serviceRecipientId,
                this.handler.getHandler().getPreSurveyQuestionsOutstanding(),
                this.handler.getHandler().getPostSurveyQuestionsOutstanding(),
                () => onCompleted(),
                () => onCompleted());
        } else {
            onCompleted();
        }
    }

    /**
     * Independently triggering the wizard
     */
    private triggerWizard() {
        hactWizardForm.showHactInModalByIds(
            this.sessionData,
            this.serviceRecipientId,
            this.handler.getHandler().getPreSurveyQuestionsOutstanding(),
            this.handler.getHandler().getPostSurveyQuestionsOutstanding(),
            () => this.submitted(),
            () => {return;}); // do nothing on cancel
    }

    private submitted() {
        this.load();
    }

    public showInModalHistory(clientId: number, svcrepId: number) {
        const historyControl = new QuestionnaireHistoryListControl(svcrepId,
            EvidenceGroup.hactQuestionnaire,
            clientId);
        showFormInModalDom(historyControl);
        historyControl.load();
    }

}

export default HactNotificationControl;
