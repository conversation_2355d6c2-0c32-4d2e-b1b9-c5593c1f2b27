package com.ecco.webApi.evidence;

import lombok.Data;
import org.jetbrains.annotations.Nullable;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import java.util.UUID;

/**
 * Base class for a change to some SMART step evidence
 */
@Data
public class BaseActionViewModel {

    /**
     * The ID for this change.
     */
    public Long id;

    public UUID workId;

    public LocalDateTime workDate;

    /**
     * The target date of the SMART step.
     * <p/>
     * If this change modifies the target date, then this field contains the
     * target date as modified by this change. Otherwise, it contains the
     * unmodified target date.
     */
    @Nullable
    public LocalDateTime targetDateTime;

    @Nullable
    public LocalDate expiryDate;

    /**
     * The SMART step definition name for display purposes.
     */
    public String name;

    /** Optional specific goal name for the client on this SMART step. */
    public String goalName;

    /** Optional specific detail on how to achieve the goal for the client on this SMART step. */
    public String goalPlan;
    /** Score 0-10 (or null) for how well someone feels they have progressed */
    public Integer score;

    /**
     * The status of the SMART step.
     * <p/>
     * <dl>
     * <dt>0</dt><dd>unsetRelevant</dd>
     * <dt>1</dt><dd>isRelevant</dd>
     * <dt>2</dt><dd>notRelevant</dd>
     * <dt>3</dt><dd>achieved</dd>
     * <dt>4</dt><dd>unAchieved</dd>
     * <dt>4</dt><dd>commentOnly</dd>
     * </dl>
     */
    public int status;

    /** Indicates if the status has changed */
    public boolean statusChange;

    /** The id of the list definition relation to the reason of the status change */
    public Integer statusChangeReasonId;

    /** Entity id for definition of the Action */
    public Long actionId;

    /** Entity id for definition of the ActionGroup */
    public Long actionGroupId;

    /** Entity id for definition of the Outcome */
    public Long outcomeId;

    /** The action instance (multiple & repeating SMART steps) */
    public UUID actionInstanceUuid;
    /** The action instance parent reference, if any - allows hierarchical smart steps */
    public UUID parentActionInstanceUuid;
    /** The hierarchy level of the smart step (0 - root, 1 - inset 1, 2 - inset 2... */
    public Short hierarchy;
    /** The order of the instance within its root hierarchy (0 - based) */
    public String position;
}
