package com.ecco.webApi.evidence;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.EvidenceAction;
import com.ecco.dom.EvidenceSupportAction;
import com.ecco.dom.EvidenceWork;
import com.ecco.dom.commands.GoalUpdateCommand;
import com.ecco.evidence.ParentChildResolver;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.service.TaskDefinitionService;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.calendar.core.CalendarService;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.io.Serializable;
import java.time.LocalDateTime;

import static com.ecco.infrastructure.time.JodaToJDKAdapters.UTC;
import static com.ecco.security.SecurityUtil.getUser;

public abstract class BaseGoalCommandHandler<VM extends BaseGoalUpdateCommandViewModel> extends
        EvidenceCommandHandler<VM, GoalUpdateCommand, GoalParams> {

    public BaseGoalCommandHandler(ObjectMapper objectMapper, ServiceRecipientRepository serviceRecipientRepository,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            ServiceRepository serviceRepository,
            ParentChildResolver parentChildResolver,
            @Nonnull CalendarService calendarService,
            @Nonnull TaskDefinitionService taskDefinitionService,
            @Nonnull EntityUriMapper entityUriMapper,
            Class<VM> vmClass) {
        super(objectMapper, serviceRecipientRepository, serviceRecipientCommandRepository, serviceRepository,
                parentChildResolver, calendarService, taskDefinitionService, entityUriMapper, vmClass);
    }

    @Override
    protected GoalUpdateCommand createCommand(Serializable targetId, GoalParams params, String requestBody, VM viewModel, long userId) {
        Assert.state(params.serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");
        Assert.state(params.actionDefId == viewModel.actionDefId, "actionDefId in body must match URI");

        return new GoalUpdateCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, params.serviceRecipientId,
                params.actionDefId, params.evidenceGroupKey, params.taskName);
    }

    /** Pre-requisites are that we have a previous snapshot from which to get data that hasn't changed, and that
     * the newSnapshot already has work attached
     */
    protected <T extends EvidenceWork> void applyCommonUpdates(Authentication authentication, VM vm,
                                                               EvidenceAction<T> previousSnapshot, EvidenceAction<T> newSnapshot) {
        Assert.notNull(previousSnapshot);
        Assert.notNull(newSnapshot.getWork());

        newSnapshot.setCreated(vm.timestamp.toDateTime(DateTimeZone.UTC));

        // Copy work date to action from GenericTypeWork
        newSnapshot.setWorkDate(newSnapshot.getWork().getWorkDate());

        newSnapshot.setAuthor(getUser(authentication).getContact());

        if (vm.statusChange != null && vm.statusChange.from != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.statusChange, previousSnapshot.getStatus(), "status");

            var noPreviousSnapshot = previousSnapshot.equals(newSnapshot);

            // for new commentOnly, we should probably have no statusChange because elsewhere we assume a statusChange is a goal
            if (noPreviousSnapshot && vm.statusChange.to == EvidenceSupportAction.commentOnly) {
                newSnapshot.setStatusChange(false);
            } else {
                newSnapshot.setStatusChange(true);
            }

            newSnapshot.setStatus(vm.statusChange.to);
        }

        if (Boolean.TRUE.equals(vm.forceStatusChange)) {
            // for multi-achieved where status can be same as prev but we're recording the event
            newSnapshot.setStatusChange(true);
        }

        // status and force change implies checks/checklist - so we move the targetDate on
        if (vm.statusChange != null && vm.statusChange.to != null && Boolean.TRUE.equals(vm.forceStatusChange)) {
            // if checks/checklist, then update the target date according to any schedule
            if (vm.statusChange.to == EvidenceSupportAction.achievedAndStillRelevant) {
                newSnapshot.setStatus(vm.statusChange.to); // NOTE: Previously null -> aASR status could leave status still as 1.
                var origTargetSchedule = ((EvidenceSupportAction) previousSnapshot).getTargetSchedule();
                var workDateOrCreated = newSnapshot.getWorkDate() != null
                        ? newSnapshot.getWorkDate().toLocalDateTime()
                        : newSnapshot.getCreated().toLocalDateTime();
                var workDateOrCreatedJdk = JodaToJDKAdapters.localDateTimeToJDk(workDateOrCreated);
                if (origTargetSchedule != null) {
                    var targetDate = Schedule.from(origTargetSchedule).nextDateTimeAfter(workDateOrCreatedJdk, Schedule.SettingUpOrRecording.RECORDING);
                    newSnapshot.setTarget(targetDate != null ? JodaToJDKAdapters.dateTimeToJoda(targetDate.atZone(UTC)) : null);
                }
            }
        } else {
            if (vm.targetDateChange != null) {
                warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.targetDateChange, previousSnapshot.getTarget(), "target date");
                // targetDate localdate into UTC
                newSnapshot.setTarget(asUTCDateTimeOrNull(vm.targetDateChange.to));
            }
        }

        // targetSchedules should not just be set to null - use end date to stop them
        if (vm.targetScheduleChange != null && vm.targetScheduleChange.to != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.targetScheduleChange, ((EvidenceSupportAction) previousSnapshot).getTargetSchedule(), "target schedule");
            ((EvidenceSupportAction) newSnapshot).setTargetSchedule(vm.targetScheduleChange.to);

            // calculate the next date - which needs to be after the work date
            DateTime scheduleAfter = newSnapshot.getWork().getWorkDate();
            // if we also specify 'after the current target date' to try to ensure we don't repeat our checks (from DEV-1640),
            // then we may miss near-future dates, and we should only need to check the target date when we are setting up a check
            // however, for now we just set the nextDateTimeAfter above from the work date, ignoring the current target date.
            //if (newSnapshot.getTarget() != null && newSnapshot.getTarget().isAfter(scheduleAfter)) {
            //    scheduleAfter = newSnapshot.getTarget();
            //}
            LocalDateTime scheduleAfterJdk = JodaToJDKAdapters.localDateTimeToJDk(scheduleAfter.toLocalDateTime());

            // set the next due
            var schedule = Schedule.from(vm.targetScheduleChange.to);
            LocalDateTime nextScheduleDue = getNextScheduleDateWhenEditingSchedule(scheduleAfterJdk, schedule);

            var nextScheduleDueJoda = nextScheduleDue != null ? JodaToJDKAdapters.dateTimeToJoda(nextScheduleDue.atZone(UTC)) : null;
            newSnapshot.setTarget(nextScheduleDueJoda);
        }

        if (vm.expiryDateChange != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.expiryDateChange, previousSnapshot.getTarget(), "expiry date");
            newSnapshot.setExpiryDate(asUTCDateTimeOrNull(vm.expiryDateChange.to));
        }

        if (vm.goalNameChange != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.goalNameChange, previousSnapshot.getGoalName(), "goalName");
            newSnapshot.setGoalName(vm.goalNameChange.to);
        }

        if (vm.goalPlanChange != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.goalPlanChange, previousSnapshot.getGoalPlan(), "goalPlan");
            newSnapshot.setGoalPlan(vm.goalPlanChange.to);
        }

        if (vm.scoreChange != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.scoreChange, previousSnapshot.getScore(), "score");
            newSnapshot.setScore(vm.scoreChange.to);
        }

        if (vm.hierarchyChange != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.hierarchyChange, previousSnapshot.getHierarchy(), "hierarchy");
            newSnapshot.setHierarchy(vm.hierarchyChange.to);
        }

        if (vm.positionChange != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.positionChange, previousSnapshot.getPosition(), "position");
            newSnapshot.setPosition(vm.positionChange.to);
        }

    }

    /**
     * Determine the next schedule date, or null if there isn't one.
     * @param fromDate The datetime to calculate the next date after this (not matching)
     * @param schedule The schedule to use to get the next date
     * @return The next date, if there is one available from the rules
     */
    @Nullable
    private LocalDateTime getNextScheduleDateWhenEditingSchedule(LocalDateTime fromDate, Schedule schedule) {
        if (schedule == null) {
            return null;
        }
        return schedule.nextDateTimeAfter(fromDate, Schedule.SettingUpOrRecording.SETTING_UP);
    }

}
