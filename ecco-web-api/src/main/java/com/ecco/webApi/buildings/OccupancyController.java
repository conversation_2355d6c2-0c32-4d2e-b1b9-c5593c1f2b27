package com.ecco.webApi.buildings;

import com.ecco.dom.ReportCriteriaDto;
import com.ecco.webApi.contacts.occupancy.OccupancyHistoryViewModel;
import com.ecco.webApi.controllers.ReportUnsecuredDelegator;
import org.joda.time.LocalDate;
import org.joda.time.format.ISODateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static org.springframework.format.annotation.DateTimeFormat.ISO.DATE;

/**
 * Controller for managing user notifications
 */
@RestController
@RequestMapping("/occupancyhistory")
public class OccupancyController {

    private static final Logger log = LoggerFactory.getLogger(OccupancyController.class);

    private final ReportUnsecuredDelegator reportUnsecuredDelegator;

    @Autowired
    public OccupancyController(
            ReportUnsecuredDelegator reportUnsecuredDelegator) {
        this.reportUnsecuredDelegator = reportUnsecuredDelegator;
    }

    /**
     * Get occupancy history
     */
    @GetJson("/")
    public List<OccupancyHistoryViewModel> getOccupancyHistory(
            @RequestParam @DateTimeFormat(iso = DATE) LocalDate from,
            @RequestParam @DateTimeFormat(iso = DATE) LocalDate to,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer[] buildingIds) {

        ReportCriteriaDto dto = new ReportCriteriaDto();
        dto.setFrom(from.toString(ISODateTimeFormat.date()));
        dto.setTo(to.toString(ISODateTimeFormat.date()));
        dto.setBuildingIds(buildingIds);

        return this.reportUnsecuredDelegator.occupancyHistory(page, dto);
    }

}
