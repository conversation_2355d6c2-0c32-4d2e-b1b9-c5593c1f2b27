package com.ecco.dom;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.infrastructure.Created;
import com.ecco.serviceConfig.dom.Action;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.springframework.format.annotation.DateTimeFormat;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@MappedSuperclass
public abstract class EvidenceAction<WORK extends EvidenceWork> extends BaseEvidence
        implements EvidenceData, Created {

    private static final long serialVersionUID = 1L;

    // see also GenericTypeDisplayAction
    public static int unsetRelevant = 0, isRelevant = 1, notRelevant = 2;
    public static int achieved = 3, unAchieved = 4, achievedAndStillRelevant = 5;
    public static int commentOnly = 6;

    // referralActivity
    // see also GenericTypeDisplayAction
    public static int noActivities = 0, hasActivity = 1, hasActivities = 2;
    @NotNull
    @Column(name = "actionInstanceUuid", nullable = false, columnDefinition = "CHAR(36)")
    @Type(type = "uuid-char")
    protected UUID actionInstanceUuid;
    @Nullable
    @Column(name = "parentActionInstanceUuid", columnDefinition = "CHAR(36)")
    @Type(type = "uuid-char")
    protected UUID parentActionInstanceUuid;
    @Nullable
    @Column
    protected Short hierarchy;
    @Nullable
    @Column
    protected String position;

    @ManyToOne(fetch=FetchType.EAGER) // FIXME: Migrate to only needing actionId. This is eager to maintain EAGER behaviour that @NotFound forced
    @Nonnull
    @JoinColumn(name="actionId")
    protected Action action;

    @Column(insertable = false, updatable = false)
    protected Long actionId;

    @Column
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @DateTimeFormat(style="S-")
    protected DateTime target;

    @Column
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @DateTimeFormat(style="S-")
    protected DateTime expiryDate;

    @Column(name = "goalName")
    protected String goalName;

    @Lob
    @Column(name = "goalPlan")
    protected String goalPlan;

    protected Integer score;

    @Column
    protected int activity;

    protected int status;
    // now that we want more fields in the 'audit history' (eg target date)
    // it can be that now a status doesn't change, so we need to know that
    // for the tools which show/measure history
    // the same audit is used to determine the latest support plan
    // and so the status can't simply be zerod
    protected boolean statusChange;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "statusChangeReasonId")
    protected ListDefinitionEntry statusChangeReason;

    // many to one since we can have many riskActionComments to one contact
    @ManyToOne(fetch=FetchType.EAGER) // FIXME: we probably only need a contact view with first/last/orgName/id
    @JoinColumn(name="contactId")
    protected ContactImpl author;


    public EvidenceAction(int serviceRecipientId) {
        super(serviceRecipientId);
    }


    public abstract WORK getWork();


    public void accept(EvidenceActionVisitor visitor) {
        visitor.visit(this);
    }

    public DateTime getDate() {
        return getCreated();
    }

    @Override
    public int compareTo(EvidenceDataAcceptor o) {
        return getWorkId().compareTo(o.getWorkId());
        //return this.getCreated().compareTo(o.getDate());
    }
}

