import {Building, OccupancyHistoryDto} from "../building-dto";
import {EccoDate, NumberToObjectMap} from "@eccosolutions/ecco-common";

export interface BuildingRepository {
    findOneBuilding(buildingId: number): Promise<Building>;

    findOneBuildingBySrId(srId: number): Promise<Building>;

    findAllBuildingsInIds(ids: number[]): Promise<Building[]>;

    findAllBuildingCareRuns(buildingId: number): Promise<Building[]>;

    /**
     * @param query.resourceType defaults to BUILDING resource type when omitted
     * @param query.showChildren defaults to true.  Include rooms/runs etc at this location
     */
    findAllBuildings(query?: {
        resourceType?: string;
        showChildren?: "true" | "false";
    }): Promise<Building[]>;

    /**
     * @param query.resourceType defaults to BUILDING resource type when omitted
     * @param query.showChildren defaults to true.  Include rooms/runs etc at this location
     */
    findAllBuildingsForUser(query?: {
        resourceType?: string;
        showChildren?: "true" | "false";
    }): Promise<Building[]>;

    findAllBuildingsOfLocationId(addressLocationId: number): Promise<Building[]>;

    getCachedBuildingsMap(refresh?: boolean): Promise<NumberToObjectMap<Building>>;

    findOccupancyHistory(
        from: EccoDate,
        to: EccoDate,
        page?: number,
        buildingIds?: number[]
    ): Promise<OccupancyHistoryDto[]>;
}
