import {mount} from "@cypress/react";
import * as React from "react";
import {sessionData} from "../../__tests__/testUtils";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {AddressHistoryDto, BuildingAjaxRepository, BuildingRepository} from "ecco-dto";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {OccupancyList} from "../../buildings/OccupancyList";
import {EccoDate} from "@eccosolutions/ecco-common";

describe("OccupancyList tests", () => {
    it("render page", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <OccupancyList />
            </TestServicesContextProvider>
        );
        cy.viewport(1250, 750);
    });
});

const overrides = {
    getBuildingRepository: () => buildingRepository,
    sessionData: sessionData
} as EccoAPI;

const buildingRepository = getFailAllMethodsMock<BuildingRepository>(BuildingAjaxRepository);
buildingRepository.findOccupancyHistory = (
    from: EccoDate,
    to: EccoDate,
    page?: number,
    buildingIds?: number[]
) => Promise.resolve(addressHistoryDtos);

// from /housing
const addressHistoryDtos: AddressHistoryDto[] = [
    {
        id: 1332,
        serviceRecipientId: 200299,
        validFrom: "2021-09-01T00:00:00",
        validTo: "2024-08-19T00:00:00",
        //"contactId": 111639,
        buildingId: 1084,
        addressId: 1330
    },
    {
        id: 1333,
        serviceRecipientId: 200299,
        validFrom: "1970-01-01T00:00:00",
        validTo: "2021-09-01T00:00:00",
        //"contactId": 111639,
        buildingId: null,
        addressId: 1224
    }
];

// buildings
/*export const buildingTestData: Building[] = [
    {
        buildingId: 1065,
        name: "Hebden Avenue",
        disabled: false,
        externalRef: null,
        resourceTypeId: 135,
        resourceTypeName: "leased property",
        serviceRecipientId: 200002,
        calendarId: "dd00006c-0467-4c4e-a0d2-8e079c3b44cf",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: 1082,
        address: {
            addressId: 1082,
            address: ["Extra care service", null, null],
            town: null,
            county: null,
            postcode: "SK2 2SK",
            disabled: false
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/workers:all/view?demandFilter=buildings:1065&loadResource=true&loadDemand=true{&startDate,endDate}"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/agreements/serviceRecipient/200002/"
            }
        ]
    },
    {
        buildingId: 1067,
        name: "Far Dene",
        disabled: false,
        externalRef: null,
        resourceTypeId: 135,
        resourceTypeName: "leased property",
        serviceRecipientId: 200003,
        calendarId: "da5c0da1-7698-4e51-a1eb-aef960930eac",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: null,
        address: null,
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/workers:all/view?demandFilter=buildings:1067&loadResource=true&loadDemand=true{&startDate,endDate}"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/agreements/serviceRecipient/200003/"
            }
        ]
    },
    {
        buildingId: 1069,
        name: "Cherry Tree House",
        disabled: false,
        externalRef: null,
        resourceTypeId: 135,
        resourceTypeName: "leased property",
        serviceRecipientId: 200004,
        calendarId: "94006d96-960b-4dd2-afa3-8951789c35a2",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: 1077,
        address: {
            addressId: 1077,
            address: ["11 Cherry Tree Walk", null, null],
            town: "Stockport",
            county: null,
            postcode: "SK3 3SK",
            disabled: false
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/workers:all/view?demandFilter=buildings:1069&loadResource=true&loadDemand=true{&startDate,endDate}"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/agreements/serviceRecipient/200004/"
            }
        ]
    },
    {
        buildingId: 1071,
        name: "Miry Lane",
        disabled: false,
        externalRef: null,
        resourceTypeId: 135,
        resourceTypeName: "leased property",
        serviceRecipientId: 200005,
        calendarId: "2bd381b4-3ada-466a-908a-cfb480cb9b4f",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: null,
        address: null,
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/workers:all/view?demandFilter=buildings:1071&loadResource=true&loadDemand=true{&startDate,endDate}"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/agreements/serviceRecipient/200005/"
            }
        ]
    }
];*/
