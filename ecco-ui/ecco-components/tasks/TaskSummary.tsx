import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {
    AcceptedState,
    Agency,
    Individual,
    ReferralSummaryDto,
    ReferralSummaryWithEntities,
    ReviewChoices,
    SessionData,
    TaskNames
} from "ecco-dto";
import * as React from "react";
import {Component, FunctionComponent, useEffect} from "react";
import {TaskAudit} from "./TaskAudit";
import {useServicesContext} from "../ServicesContext";
import {
    ServiceRecipientWithEntitiesContext,
    useCurrentLatestCommands,
    useCurrentServiceRecipientWithEntities
} from "../data/serviceRecipientHooks";
import {useAgency, useIndividual} from "../data/entityLoadHooks";


interface Props {
    referral: ReferralSummaryWithEntities;
    sessionData: SessionData;
    onHasAudit?: (hasAudit: boolean) => void;
}

interface PropsWithTask extends Props {
    taskName: string;
}

/**
 * Finds the latest 'dateSelector' across all referrals regardless of security of the user (eg ACLs)
 */
function getReferralWithoutSecurityWithLatest(
    currentReferral: ReferralSummaryWithEntities,
    dateSelector: (r: ReferralSummaryDto) => string | null
) {
    return TaskAudit.instance
        .getClientReferralsWithoutSecurityQ(false, currentReferral.serviceRecipientId)
        .then(clientReferrals =>
            clientReferrals.reduce((prev, curr) => {
                // ReferralServiceImpl.setReferralWithDataProtectionSignature saves with utc 'now'
                let prevAgreed = prev ? EccoDateTime.parseIso8601Utc(dateSelector(prev)) : null;
                let currAgreed = curr ? EccoDateTime.parseIso8601Utc(dateSelector(curr)) : null;
                return prevAgreed && currAgreed
                    ? prevAgreed.laterThan(currAgreed)
                        ? prev
                        : curr
                    : prevAgreed && !currAgreed
                    ? prev
                    : curr;
            }, currentReferral)
        );
}

const AcceptState = ({
    state,
    decisionDate,
    signpostedReason,
    onHasAudit
}: {
    state: AcceptedState;
    decisionDate?: string;
    signpostedReason: string | null;
    onHasAudit?: (hasAudit: boolean) => void;
}) => {
    let elm: JSX.Element | null = null;
    if (state != "UNSET") {
        if (state == "ACCEPTED") {
            elm = <span>yes (on {EccoDate.iso8601ToFormatShort(decisionDate!)})</span>;
        } else {
            elm = (
                <span>
                    no (on {EccoDate.iso8601ToFormatShort(decisionDate!)}) {signpostedReason}
                </span>
            );
        }
    }
    useEffect(() => {
        onHasAudit && onHasAudit(elm != null);
    });
    return elm;
};

const AssessmentDate = (props: {
    referral: ReferralSummaryDto;
    onHasAudit?: (hasAudit: boolean) => void;
}) => {
    useEffect(() => {
        props.onHasAudit && props.onHasAudit(!!props.referral.decisionDate);
    });
    return props.referral.decisionDate ? (
        <span>
            scheduled: {EccoDateTime.iso8601ToFormatShort(props.referral.decisionDate)}, dna:{" "}
            {props.referral.interviewDna}
        </span>
    ) : null;
};

export const AuditAgreement = (props: {
    agreementDate?: string;
    agreementStatus?: boolean;
    agreementSignedId?: string;
    rId?: number;
    onHasAudit?: (hasAudit: boolean) => void;
}) => {
    // agreementDate is the key to knowing if its been 'done' - as per signedAgreement's 'hasAgreement'
    const hasAudit = !!props.agreementDate;
    useEffect(() => {
        props.onHasAudit && props.onHasAudit(hasAudit);
    });

    const messages = useServicesContext().sessionData.getMessages();

    if (!hasAudit) {
        return null;
    }

    let text =
        typeof props.agreementStatus != "boolean"
            ? "completed"
            : props.agreementStatus
            ? "accepted"
            : "rejected";
    if (props.rId) {
        text = text.concat(` [r-id ${props.rId}]`);
    }
    const sigMsg = messages["form.agreements.signature"];
    let signedText = (props.agreementSignedId ? "with " : "without ").concat(sigMsg);

    // NB The incoming UTC date time is parsed as UTC, then passed to moment to convert to local and print
    // We avoid creating another internal date representation, but we could - see other files in the same commit.
    return (
        <span>
            {text} {EccoDateTime.iso8601UtcToFormatLocalShortDate(props.agreementDate || null)}{" "}
            {signedText}
        </span>
    );
};

const AuditDataProtection = (props: {
    srId: number;
    referral: ReferralSummaryDto;
    onHasAudit?: (hasAudit: boolean) => void;
}) => {
    return (
        <AuditAgreement
            rId={
                props.srId != props.referral.serviceRecipientId
                    ? props.referral.referralId
                    : undefined
            }
            agreementDate={props.referral.dataProtectionAgreementDate}
            agreementStatus={props.referral.dataProtectionAgreementStatus}
            agreementSignedId={props.referral.dataProtectionSignedId}
            onHasAudit={props.onHasAudit}
        />
    );
};

class DataProtectionSummary extends Component<Props, {latestReferral: ReferralSummaryDto}> {
    private readonly scopeClient: boolean;

    constructor(props: Props) {
        super(props);
        this.scopeClient = props.referral.configResolver
            .getServiceType()
            .taskDefinitionSettingHasFlag(TaskNames.dataProtection, "scope", "client");
        this.state = {latestReferral: this.props.referral};
    }

    componentDidMount(): void {
        if (this.scopeClient) {
            getReferralWithoutSecurityWithLatest(
                this.props.referral,
                r => r.dataProtectionAgreementDate!!
            ).then(latestReferral => {
                this.setState({latestReferral});
            });
        }
    }

    render() {
        return (
            <AuditDataProtection
                srId={this.props.referral.serviceRecipientId}
                referral={this.state.latestReferral}
                onHasAudit={this.props.onHasAudit}
            />
        );
    }
}

const FundingState: FunctionComponent<{
    fundingAccepted: boolean;
    fundingSource: string | null;
    fundingDecisionDate?: string;
    fundingHoursOfSupport?: number;
    onHasAudit?: (hasAudit: boolean) => void;
}> = ({fundingAccepted, fundingSource, fundingDecisionDate, fundingHoursOfSupport, onHasAudit}) => {
    const parts = new Array<string>();

    if (fundingDecisionDate) {
        parts.push(
            (fundingAccepted ? "granted: " : "scheduled: ") +
                EccoDate.iso8601ToFormatShort(fundingDecisionDate)
        );
    }

    if (fundingSource) {
        parts.push(`from:  ${fundingSource}`);
    }

    if (fundingHoursOfSupport && fundingHoursOfSupport > 0) {
        parts.push(`hours:  ${fundingHoursOfSupport}`);
    }

    const elm = parts.length == 0 ? null : <span>{parts.join(", ")}</span>;
    useEffect(() => {
        onHasAudit && onHasAudit(elm != null);
    });
    return elm;
};

const PendingStatusSummary = (props: Props) => {
    const elm = props.referral.pendingStatusId ? (
        <span>
            status:{" "}
            {props.sessionData
                .getListDefinitionEntryById(props.referral.pendingStatusId)
                .getDisplayName() || props.referral.pendingStatusId}
        </span>
    ) : null;
    useEffect(() => {
        props.onHasAudit && props.onHasAudit(elm != null);
    });
    return elm;
};

const ReferralSource: FunctionComponent<{
    selfReferral: boolean;
    referrer: Individual | null;
    agency: Agency | null;
    onHasAudit?: (hasAudit: boolean) => void;
}> = ({selfReferral, referrer, agency, onHasAudit}) => {
    let elm: JSX.Element | null;
    if (selfReferral) {
        elm = <span>self referral</span>;
    } else {
        let referrerStr = "";
        if (referrer) {
            referrerStr =
                (referrer.firstName ? referrer.firstName + " " : "") +
                (referrer.lastName ? referrer.lastName + " " : "") +
                (referrer.mobileNumber ? referrer.mobileNumber + " " : "") +
                (referrer.phoneNumber ? referrer.phoneNumber : "");
        }
        if (agency) {
            referrerStr =
                (referrerStr ? referrerStr + " - " : "") +
                (agency.companyName ? agency.companyName + " " : "") +
                (agency.mobileNumber ? agency.mobileNumber + " " : "") +
                (agency.phoneNumber ? agency.phoneNumber + " " : "") +
                (agency.email ? agency.email : "");
        }
        elm = !referrerStr ? null : <span>{referrerStr}</span>;
    }
    useEffect(() => {
        onHasAudit && onHasAudit(elm != null);
    });
    return elm;
};

const StartSummary: FunctionComponent<{
    receivingServiceDate?: string;
    onHasAudit?: (hasAudit: boolean) => void;
}> = ({receivingServiceDate, onHasAudit}) => {
    const messages = useServicesContext().sessionData.getMessages();
    // return if we have no date - no useful audit (assigned worker can still be seen elsewhere)
    let elm: JSX.Element | null = null;
    if (receivingServiceDate) {
        const dte = EccoDate.parseIso8601(receivingServiceDate); // local, user time - see PredicateSupport.isSystemField
        const startedTxt = dte.earlierThanOrEqual(EccoDate.todayLocalTime())
            ? messages["status.started"]
            : messages["status.toStart"];
        elm = (
            <span>
                {startedTxt} {dte.formatShort()}
            </span>
        );
    }
    useEffect(() => {
        onHasAudit && onHasAudit(elm != null);
    });
    return elm;
};

class ScheduleReviewsSummary extends Component<Props, {reviewChoices: ReviewChoices | null}> {
    constructor(props: Props) {
        super(props);
        this.state = {reviewChoices: null};
    }

    componentDidMount(): void {
        TaskAudit.instance
            .getReviewChoicesQ(false, this.props.referral.serviceRecipientId)
            .then(reviewChoices => {
                this.setState({reviewChoices});
                const choices = this.state.reviewChoices;
                const reviewDate = choices?.dto?.nextReviewDate;
                this.props.onHasAudit && this.props.onHasAudit(reviewDate != null);
            });
    }

    // render according to overview/scheduleReviews.jsp
    // which simply shows the next few review dates (we no longer use the 'all' option)
    // however, we don't want to get the dates from the calendar anymore for several reasons...
    //      1 - we currently enter each date, and so have put a hard limit of 2 years (which is causing issues)
    //      2 - a calendar entry in the client (and user) isn't the best idea, it needs to be a task+when scenario
    // therefore here we are going to show the next review date only
    // if requested, we can do calculations to show the others client-side, and even show review history etc here
    // nextReviewDate comes from ReviewChoicesToViewModel.java
    render() {
        const choices = this.state.reviewChoices;
        // TODO: what if there are no choices?
        return choices ? (
            choices && choices.dto && choices.dto.nextReviewDate ? (
                <span>
                    next review:{" "}
                    <span>{EccoDate.iso8601ToFormatShort(choices.dto.nextReviewDate)}</span>
                </span>
            ) : null
        ) : null;
    }
}

class ReviewSummary extends Component<PropsWithTask, {reviewChoices: ReviewChoices | null}> {
    constructor(props: PropsWithTask) {
        super(props);
        this.state = {reviewChoices: null};
    }

    componentDidMount(): void {
        TaskAudit.instance
            .getReviewChoicesQ(false, this.props.referral.serviceRecipientId)
            .then(reviewChoices => {
                this.setState({reviewChoices});
                this.props.onHasAudit && this.props.onHasAudit(reviewChoices != null);
            });
    }

    summary() {
        if (!(this.state.reviewChoices && this.state.reviewChoices.dto)) {
            return null;
        }
        const reviewChoices = this.state.reviewChoices;

        // render according to what was in referralView_tasks
        if (reviewChoices.dto.incompleteReviewId) {
            const totalOutcomes = this.props.referral.configResolver
                .getServiceType()
                .getOutcomesForTaskName(this.props.taskName).length;
            const pcCompleted = reviewChoices.getPercentComplete(totalOutcomes);
            const currentReviewDate = EccoDate.parseIso8601(reviewChoices.dto.currentReviewDate); // see ReviewScheduleControl.ts
            const currentReviewOverdue = currentReviewDate.earlierThan(EccoDate.todayLocalTime());

            return (
                <span>
                    current review:&nbsp;
                    <span style={(currentReviewOverdue && {color: "red"}) || {}}>
                        {currentReviewDate.formatShort()} {pcCompleted.toString()}%
                    </span>
                </span>
            );
        }

        const lastReviewDate = EccoDate.parseIso8601(reviewChoices.dto.lastReviewDate);
        return lastReviewDate ? <span>last review: {lastReviewDate.formatShort()}</span> : null;
    }

    render() {
        const choices = this.state.reviewChoices;
        // TODO: what if there are no choices?
        return choices ? this.summary() : null;
    }
}

const LatestCommandSummary: FunctionComponent<PropsWithTask> = ({
    taskName,
    onHasAudit
}) => {
    const {audits: latestCmds} = useCurrentLatestCommands();
    useEffect(() => {
        onHasAudit && onHasAudit(cmd != null);
    }, []);
    const cmd = latestCmds[taskName];
    return cmd ? (
        <span>
            last update: {EccoDateTime.iso8601UtcToFormatLocalShort(cmd.timestamp)} by{" "}
            {cmd.userName}
        </span>
    ) : null;
};

const CloseSummary: FunctionComponent<{
    reviewDate: string | null;
    exitedDate: string | null;
    exitReasonName: string | null;
    onHasAudit?: (hasAudit: boolean) => void;
}> = ({reviewDate, exitedDate, exitReasonName, onHasAudit}) => {
    const messages = useServicesContext().sessionData.getMessages();

    let elm: JSX.Element | null = null;
    if (exitedDate) {
        const status = reviewDate != null ? "review date" : messages["status.exited"];
        const dte = EccoDate.parseIso8601(exitedDate) || EccoDate.parseIso8601(reviewDate); // local, user time - see PredicateSupport.isSystemField
        elm = (
            <span>
                {status} {dte.formatShort() + (exitReasonName ? ": ".concat(exitReasonName) : "")}
            </span>
        );
    }
    useEffect(() => {
        onHasAudit && onHasAudit(elm != null);
    });
    return elm;
};

function AgencySummary({
    referral,
    onHasAudit
}: {
    referral: CommonSourceDto;
    onHasAudit?: (hasAudit: boolean) => void;
}) {
    const {agency} = useAgency(referral.referrerAgencyId || null);
    const {contact} = useIndividual(referral.referrerIndividualId || null);
    const hasAudit = !!(contact || agency || referral.selfReferral);
    useEffect(() => {
        onHasAudit && onHasAudit(hasAudit);
    });
    return hasAudit ? (
        <ReferralSource
            selfReferral={referral.selfReferral || false}
            referrer={contact || null}
            agency={agency || null}
        />
    ) : null;
}

function ProjectSummary({
    referral,
    onHasAudit
}: {
    referral: ReferralSummaryWithEntities;
    onHasAudit?: (hasAudit: boolean) => void;
}) {
    const elm = referral.serviceAllocationId ? (
        <span>{referral.features.getProjectName(referral.features.getServiceCategorisation(referral.serviceAllocationId).projectId)}</span>
    ) : null;
    useEffect(() => {
        onHasAudit && onHasAudit(elm != null);
    });
    return elm;
}

interface CommonSourceDto {
    selfReferral?: boolean;
    //source: string;
    referrerAgencyId?: number;
    referrerIndividualId?: number;
}
interface CommonReferralDetailsDto {
    receivedDate?: string;
}
interface CommonAcceptDto {
    acceptOnServiceState: AcceptedState;
    decisionMadeOn?: string;
    signpostedReasonId?: number;
}
interface CommonCloseDto {
    exitedDate?: string;
    exitReasonId?: number;
    reviewDate?: string;
}
interface CommonStartDto {
    receivingServiceDate?: string;
}

function taskSummaryFor(
    sessionData: SessionData,
    context: ServiceRecipientWithEntitiesContext,
    taskName: string,
    onHasAudit?: (hasAudit: boolean) => void,
    useLatestAudit?: boolean
) {
    const svcRec = context.serviceRecipient;

    // HACK
    const referral = svcRec as ReferralSummaryWithEntities;

    let dtoSource: CommonSourceDto = {} as CommonSourceDto;
    switch (context.serviceRecipient.prefix) {
        case "i":
            dtoSource = context.incident!;
            break;
        case "m":
            dtoSource = context.repair!;
            break;
        case "r":
            dtoSource = context.referral!;
            break;
    }

    let dtoReferralDetails: CommonReferralDetailsDto = {} as CommonReferralDetailsDto;
    switch (context.serviceRecipient.prefix) {
        case "i":
            dtoReferralDetails = context.incident!;
            break;
        case "m":
            dtoReferralDetails = context.repair!;
            break;
        case "r":
            dtoReferralDetails = context.referral!;
            break;
    }

    let dtoAccept: CommonAcceptDto = {} as CommonAcceptDto;
    switch (context.serviceRecipient.prefix) {
        case "i":
            dtoAccept = {
                acceptOnServiceState: context.incident!.acceptOnServiceState,
                decisionMadeOn: context.incident!.decisionMadeOn,
                signpostedReasonId: context.incident!.signpostedReasonId
            };
            break;
        case "m":
            dtoAccept = {
                acceptOnServiceState: context.repair!.acceptOnServiceState,
                decisionMadeOn: context.repair!.decisionMadeOn,
                signpostedReasonId: context.repair!.signpostedReasonId
            };
            break;
        case "r":
            dtoAccept = {
                acceptOnServiceState: context.referral!.acceptOnServiceState,
                decisionMadeOn: context.referral!.decisionMadeOn,
                signpostedReasonId: context.referral!.signpostedReasonId
            };
            break;
    }

    let dtoClose: CommonCloseDto = {} as CommonCloseDto;
    switch (context.serviceRecipient.prefix) {
        case "i":
            dtoClose = {
                exitedDate: context.incident!.exitedDate,
                exitReasonId: context.incident!.exitReasonId,
                reviewDate: context.incident!.reviewDate
            };
            break;
        case "m":
            dtoClose = {
                exitedDate: context.repair!.exitedDate,
                exitReasonId: context.repair!.exitReasonId,
                reviewDate: context.repair!.reviewDate
            };
            break;

        case "r":
            dtoClose = {
                exitedDate: context.referral!.exitedDate,
                exitReasonId: context.referral!.exitReasonId
            };
    }

    let dtoStart: CommonStartDto = {} as CommonStartDto;
    switch (context.serviceRecipient.prefix) {
        case "i":
            dtoStart = context.incident!;
            break;
        case "r":
            dtoStart = context.referral!
            break;
    }

    if (useLatestAudit) {
        return (
            <LatestCommandSummary
                referral={referral}
                sessionData={sessionData}
                taskName={taskName}
                onHasAudit={onHasAudit}
            />
        );
    }

    switch (taskName) {
        case TaskNames.assessmentDate:
            return <AssessmentDate referral={referral} onHasAudit={onHasAudit} />;
        case TaskNames.close:
            return (
                <CloseSummary
                    reviewDate={dtoClose.reviewDate || null}
                    exitedDate={dtoClose.exitedDate || null}
                    exitReasonName={
                        dtoClose.exitReasonId
                            ? sessionData
                                  .getListDefinitionEntryById(dtoClose.exitReasonId)
                                  ?.getDisplayName()
                            : null
                    }
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.consent:
            return (
                <AuditAgreement
                    agreementDate={referral.consentAgreementDate}
                    agreementStatus={referral.consentAgreementStatus}
                    agreementSignedId={referral.consentSignedId}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.dataProtection:
            return (
                <DataProtectionSummary
                    sessionData={sessionData}
                    referral={referral}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.agreement:
            return (
                <AuditAgreement
                    agreementDate={referral.agreement1AgreementDate}
                    agreementStatus={referral.agreement1AgreementStatus}
                    agreementSignedId={referral.agreement1SignedId}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.agreement2:
            return (
                <AuditAgreement
                    agreementDate={referral.agreement2AgreementDate}
                    agreementStatus={referral.agreement2AgreementStatus}
                    agreementSignedId={referral.agreement2SignedId}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.agreement3:
            return (
                <AuditAgreement
                    agreementDate={referral.agreement3AgreementDate}
                    agreementStatus={referral.agreement3AgreementStatus}
                    agreementSignedId={referral.agreement3SignedId}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.agreement4:
            return (
                <AuditAgreement
                    agreementDate={referral.agreement4AgreementDate}
                    agreementStatus={referral.agreement4AgreementStatus}
                    agreementSignedId={referral.agreement4SignedId}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.agreement5:
            return (
                <AuditAgreement
                    agreementDate={referral.agreement5AgreementDate}
                    agreementStatus={referral.agreement5AgreementStatus}
                    agreementSignedId={referral.agreement5SignedId}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.agreement6:
            return (
                <AuditAgreement
                    agreementDate={referral.agreement6AgreementDate}
                    agreementStatus={referral.agreement6AgreementStatus}
                    agreementSignedId={referral.agreement6SignedId}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.agreement7:
            return (
                <AuditAgreement
                    agreementDate={referral.agreement7AgreementDate}
                    agreementStatus={referral.agreement7AgreementStatus}
                    agreementSignedId={referral.agreement7SignedId}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.agreement8:
            return (
                <AuditAgreement
                    agreementDate={referral.agreement8AgreementDate}
                    agreementStatus={referral.agreement8AgreementStatus}
                    agreementSignedId={referral.agreement8SignedId}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.agreement9:
            return (
                <AuditAgreement
                    agreementDate={referral.agreement9AgreementDate}
                    agreementStatus={referral.agreement9AgreementStatus}
                    agreementSignedId={referral.agreement9SignedId}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.agreement10:
            return (
                <AuditAgreement
                    agreementDate={referral.agreement10AgreementDate}
                    agreementStatus={referral.agreement10AgreementStatus}
                    agreementSignedId={referral.agreement10SignedId}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.decideFinal:
            return (
                <AcceptState
                    state={dtoAccept.acceptOnServiceState}
                    decisionDate={dtoAccept.decisionMadeOn}
                    signpostedReason={
                        dtoAccept.signpostedReasonId
                            ? sessionData
                                  .getListDefinitionEntryById(dtoAccept.signpostedReasonId)
                                  ?.getDisplayName()
                            : null
                    }
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.deliveredBy:
            // NB There is a DeliveredByForm.tsx
            return null; // TODO: referral.delivererAgencyName ? <span>{referral.delivererAgencyName}</span> : null;
        case TaskNames.funding:
            return (
                <FundingState
                    fundingAccepted={referral.fundingAccepted}
                    fundingSource={
                        referral.fundingSourceId
                            ? sessionData.getFundingSourceNameById(referral.fundingSourceId)
                            : null
                    }
                    fundingDecisionDate={referral.fundingDecisionDate}
                    fundingHoursOfSupport={referral.fundingHoursOfSupport}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.needsAssessmentReductionReview:
            return (
                <ReviewSummary
                    referral={referral}
                    sessionData={sessionData}
                    taskName={taskName}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.pendingStatus:
            return (
                <PendingStatusSummary
                    sessionData={sessionData}
                    referral={referral}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.project:
        case TaskNames.projectRegion:
        case TaskNames.projectAsAccommodation:
        case TaskNames.accommodation:
            return <ProjectSummary referral={referral} onHasAudit={onHasAudit} />;
        case TaskNames.referralAccepted:
            return (
                <AcceptState
                    state={referral.appropriateReferralState}
                    decisionDate={referral.decisionReferralMadeOn}
                    signpostedReason={
                        referral.signpostedReasonId
                            ? sessionData
                                  .getListDefinitionEntryById(referral.signpostedReasonId)
                                  ?.getDisplayName()
                            : null
                    }
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.referralDetails:
            const Audit = () => {
                useEffect(() => {
                    onHasAudit && onHasAudit(dtoReferralDetails.receivedDate != null);
                }, [onHasAudit]);

                return dtoReferralDetails.receivedDate ? (
                    <span>referred: {EccoDate.iso8601ToFormatShort(dtoReferralDetails.receivedDate)}</span>
                ) : null;
            };
            return <Audit />;
        case TaskNames.scheduleReviews:
            return (
                <ScheduleReviewsSummary
                    referral={referral}
                    sessionData={sessionData}
                    onHasAudit={onHasAudit}
                />
            );
        case TaskNames.source:
        case TaskNames.sourceWithIndividual:
            return <AgencySummary referral={dtoSource} onHasAudit={onHasAudit} />;
        case TaskNames.start:
        case TaskNames.startAccommodation:
            return (
                <StartSummary
                    receivingServiceDate={dtoStart.receivingServiceDate}
                    onHasAudit={onHasAudit}
                />
            );
        default:
            return (
                <LatestCommandSummary
                    referral={referral}
                    sessionData={sessionData}
                    taskName={taskName}
                    onHasAudit={onHasAudit}
                />
            );
    }
}

export const TaskSummary = (props: {
    srId: number;
    taskName: string;
    onHasAudit?: (hasAudit: boolean) => void;
    useLatestAudit?: boolean;
}) => {
    const {resolved: context} = useCurrentServiceRecipientWithEntities();

    return taskSummaryFor(
        context!.serviceRecipient.features,
        context!,
        props.taskName,
        props.onHasAudit,
        props.useLatestAudit
    );
};

export default TaskSummary;
