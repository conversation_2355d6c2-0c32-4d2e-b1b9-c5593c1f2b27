{"name": "ecco-test-app", "version": "0.1.0", "private": true, "homepage": "Ignored - Use .env PUBLIC_URL for build target URL", "dependencies": {"@eccosolutions/ecco-common": "1.8.4", "@eccosolutions/ecco-crypto": "1.0.2", "@eccosolutions/ecco-mui": "^0.0.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^7.1.2", "@types/node": "^14.18.12", "@types/react": "^16.9.19", "@types/react-dom": "^16.9.5", "@types/react-router": "^5.1.5", "@types/react-router-dom": "^5.1.3", "application-properties": "0.0.0", "ecco-admin": "^0.0.0", "ecco-calendar": "^0.0.0", "ecco-commands": "^0.0.0", "ecco-components": "^0.0.0", "ecco-dto": "^0.0.0", "ecco-evidence": "^0.0.0", "ecco-finance": "^0.0.0", "ecco-incidents": "^0.0.0", "ecco-repairs": "^0.0.0", "ecco-forms": "^0.0.0", "font-awesome": "^4.3.0", "lazy": "^1.0.11", "react": "16.13.1", "react-dom": "16.13.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0"}, "devDependencies": {"@testing-library/react": "^12.1.4", "@types/jest": "^29.5.2", "@types/json-schema": "^7.0.11", "cross-env": "7.0.3", "customize-cra": "^1.0.0", "esbuild": "^0.17.19", "esbuild-loader": "^2.21.0", "enzyme": "^3.6.0", "enzyme-adapter-react-16": "^1.5.0", "enzyme-to-json": "^3.3.4", "jest": "^29.5.0", "jest-cli": "^29.5.0", "jest-config": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "react-app-rewired": "^2.1.5", "react-app-rewired-esbuild": "^0.0.11", "react-scripts": "4.0.3", "ts-jest": "^29.1.0", "typescript": "5.1.5", "webpack-bundle-analyzer": "^4.4.0"}, "scripts": {"analyze": "cross-env PUBLIC_URL=http://localhost:3000 BUNDLE_VISUALIZE=1 react-app-rewired start --no-cache", "start": "cross-env PUBLIC_URL=http://localhost:3000 react-app-rewired start --no-cache", "build": "react-app-rewired build", "clean": "tsc --build --clean tsconfig-composite.json", "test": "echo 'was react-app-rewired test but broken'", "eject": "react-scripts eject", "lint-fix": "eslint --fix --ext .ts,.tsx .", "lint": "eslint --ext .ts,.tsx .", "check": "tsc --build tsconfig-composite.json --verbose && echo 'Just did quick tsc check for now'", "emit": "yarn build"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}