import * as React from "react";
import {assertNotNull, CommandQueue, GoalUpdateCommand} from "ecco-commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {
    ActionComponent,
    asNumberChange,
    ConfigResolver,
    SessionData,
    SmartStepStatus,
    SupportAction
} from "ecco-dto";
import {
    ActionInstanceControlData,
    ActionInstanceControlDataSetup,
    ActionInstanceFeatures
} from "../controlDataStructures";
import {EvidenceDef} from "../domain";
import {SmartStepStatusTransitions} from "./SmartStepStatusTransitions";
import {EvidenceContext} from "../EvidenceContext";
import {useEvidencePageContext} from "../EvidencePageRoot";
import {FC, useCallback, useMemo} from "react";
import {SmartStep} from "./SmartStep";
import {SmartStepMenu} from "./Outcomes";


/******************
 * DATA STRUCTURES
 */

/**
 * Incoming/loaded/transient data
 */
export interface SmartStepData {
    snapshot: SupportAction | null;
    actionDefId?: number;
    transientOutcomeDefId?: number;
    sessionData: SessionData;
    evidenceDef: EvidenceDef;
    configResolver: ConfigResolver;
    getWorkUuid: () => Uuid;
    serviceRecipientId: number;
    readOnly: boolean;
}

/**
 * Initialised data
 */
export type SmartStepInit = {
    initData: SmartStepData
    initState: SmartStepState;

    context: EvidenceContext
    controlFeatures: ActionInstanceFeatures
    statusTransitions: SmartStepStatusTransitions
};

/**
 * SmartStep user state current data
 */
export interface SmartStepState extends ActionInstanceControlData {
    // TODO move up
    related: boolean | null; // related / link / chain
    relatedDirectClick: boolean; // remember we have specifically clicked 'related' - make it sticky
    actionDefId?: number; // reference data for this item - handy to be here
    readonly transientOutcomeDefId?: number; // reference data for this item - handy to be here
    readonly transientStatus?: SmartStepStatus | null; // reference data for this item - handy to be here
    readonly controlUuid: Uuid; // reference data for this item = handy to be here
    parentControlUuid?: Uuid; // reference data for this item = handy to be here
    editing: boolean;
}
export function cloneSmartStepState(state: SmartStepState) {
    const cloned: SmartStepState = {
        ...state,
        controlUuid: Uuid.parse(state.controlUuid.toString()),
        parentControlUuid: state.parentControlUuid
            ? Uuid.parse(state.parentControlUuid.toString())
            : undefined
    };
    return cloned;
}


/**
 * SmartStep prop data
 */
export interface SmartStepReadProps {
    init: SmartStepInit;
    state: SmartStepState;
    // NB could have done Omit<SmartStepProps, 'stateSetter'>
}
export interface SmartStepProps extends SmartStepReadProps {
    stateSetter: (update: Partial<SmartStepState>) => void;
}
export interface SmartStepRemoveProps extends SmartStepProps {
    remove: () => void;
}



/********
 * LOGIC
 */

/**
 * Create init data structure
 */
export function createSmartStepInit(
    initData: SmartStepData,
    asCreatingFromEmpty = false
): SmartStepInit {
    assertNotNull(initData.evidenceDef.getEvidencePageType(), "evidence page type is expected");

    const transitions = new SmartStepStatusTransitions(
        initData.evidenceDef.getEvidencePageType()!,
        initData.configResolver
            .getServiceType()
            .getTaskDefinitionSetting(
                initData.evidenceDef.getTaskName(),
                "allowActionStraightToAchieve"
            ) == "y"
    );
    const evidenceContext = new EvidenceContext(
        initData.serviceRecipientId,
        null,
        [],
        initData.getWorkUuid,
        initData.evidenceDef,
        transitions,
        initData.sessionData,
        initData.configResolver
    );
    // NB hierarchy 0 assumed - other hierarchies called using controlFeatures.getActionDefFeatures(this.context, this.hierarchy))
    // TODO - different configs for hierarchies
    const controlFeatures = new ActionInstanceFeatures(evidenceContext, 0);
    controlFeatures.setAllowFromBlankUi(asCreatingFromEmpty);

    const smartStepState = createSmartStepInitState(initData, asCreatingFromEmpty);

    return {
        initData: initData,
        initState: smartStepState,
        context: evidenceContext,
        controlFeatures: controlFeatures,
        statusTransitions: transitions
    };
}

export const smartStepInitLookup = (
    inits: SmartStepInit[],
    provisioned: SmartStepInit[],
    uuid: Uuid
): SmartStepInit => {
    const smartStepInit = inits.filter(s => s.initState.controlUuid.equals(uuid)).pop();
    const smartStepProvisioned = provisioned
        .filter(s => s.initState.controlUuid.equals(uuid))
        .pop();
    if (!smartStepInit && !smartStepProvisioned) {
        console.log("looking for controlUuid %o", uuid.toString());
        console.log("inside initState/smartStepProvisioned of %o", inits);
        throw new Error("smartSteps state can't find initSmartSteps - see console");
    }
    return (smartStepInit || smartStepProvisioned)!;
};

/**
 * COPIED from SupportInstanceControl
 * relate the work to this smart step (or 'undo' related)
 * @param directClick differentiates between ensuring the 'relevant' is updated because of other actions
 *      and allowing the user to attempt to toggle it - if allowed
 * @param currentRelated the state before any click/processing for this function to determine the state
 * @param isForcedRelatedState
 */
export function updateRelatedState(directClick: boolean, currentRelated: boolean, isForcedRelatedState: boolean): boolean {
    let forceOn = isForcedRelatedState; // indicate that we want to ensure the 'related' chain symbol is on
    let toggle = false;

    // do a toggle if the user is allowed
    if (directClick && !forceOn) {
        toggle = true;
    }

    // do a toggle if we are currently off but meant to be on
    if (forceOn && !currentRelated) {
        toggle = true;
    }
    // do a toggle if we were forced but are no longer
    // NB we do in the jsp version remember if the user clicked 'relevant'
    // so that if we un-set the relevant we bring back what the user did
    // but this seems a little overkill for the benefit
    if (!directClick && !forceOn && currentRelated) {
        toggle = true;
    }

    return toggle ? !currentRelated : currentRelated
}

/**
 * COPIED from SupportInstanceControl
 */
export function isForcedRelatedState(sessionData: SessionData, initialState: SmartStepState, latestState: SmartStepState): boolean {

    let forceOn = false; // indicate that we want to ensure the 'related' chain symbol is on

    // if we have a different status, we ensure it will be on
    if (latestState.status != initialState.status) {
        forceOn = true;
    }
    // if we have a different date, we ensure it will be on
    if (latestState.targetDate != initialState.targetDate
            || latestState.targetSchedule != initialState.targetSchedule
    ) {
        forceOn = true;
    }
    // if we have a different comment, we ensure it will be on
    let ignoreStatus = sessionData.isEnabled("support.evidence.goalPlanIgnoresStatus");
    if (!ignoreStatus && latestState.goalPlan != initialState.goalPlan) {
        forceOn = true;
    }

    return forceOn;
}


/**
 * Create state data structure
 */
export function createSmartStepInitState(
    initData: SmartStepData,
    asCreatingFromEmpty: boolean = false
): SmartStepState {
    const persisted = !!initData.snapshot;
    const initialState = persisted
        ? ActionInstanceControlDataSetup.fromSnapshot(initData.snapshot!, asCreatingFromEmpty, [])
        : ActionInstanceControlDataSetup.fromEmpty(asCreatingFromEmpty, []);

    // load controlUuid as the actionInstanceUuid, or new uuid
    const controlUuid =
        persisted && initData.snapshot!.actionInstanceUuid
            ? Uuid.parse(initData.snapshot!.actionInstanceUuid)
            : Uuid.randomV4();
    // load parentControlUuid as the parentActionInstanceUuid, or nothing
    const parentControlUuid =
        persisted && initData.snapshot!.parentActionInstanceUuid
            ? Uuid.parse(initData.snapshot!.parentActionInstanceUuid)
            : undefined;

    return {
        ...initialState,
        actionDefId: initData.actionDefId,
        transientOutcomeDefId: initData.transientOutcomeDefId,
        controlUuid: controlUuid,
        parentControlUuid: parentControlUuid,
        related: false,
        relatedDirectClick: false,
        editing: !persisted // if we've just created one, then we're editing
    };
}


/**
 * Create the command
 */
export const emitSmartStepCommand = (
    ref: SmartStepInit,
    state: SmartStepState,
    cmdQ: CommandQueue
) => {
    const op = ref.initState.actionInstanceUuid ? "update" : "add";
    const componentUuid = state.controlUuid;
    const parentUuid: Uuid = Uuid.parse(state.parentControlUuid?.toString());
    const cmdUuid = Uuid.randomV4();

    const actionDefId = ref.initData.actionDefId || state.actionDefId; // priorise persisted data
    if (actionDefId) {
        const cmd = new GoalUpdateCommand(
            op,
            cmdUuid,
            ref.initData.getWorkUuid(),
            ref.initData.serviceRecipientId,
            ref.initData.evidenceDef.getTaskName(),
            actionDefId,
            ref.initData.evidenceDef.getEvidenceGroup(),
            componentUuid, // controlUuid saves as the actionInstanceUuid
            parentUuid // parentControlUuid saves as the parentActionInstanceUuid
        );

        populateGoalUpdateCommand(ref, state, cmd);

        cmdQ.addCommand(cmd);
    }
};


/**
 * Work out what has changed.
 * Ideally we use initialData vs latest, however, it may be that the last control doesn't trigger a 'change'.
 * What IS important though is to not assume the control exists, and record a change when there is none
 * TODO see if the last control 'change' is triggered - so we can use initialData vs latest (and not direct control inspection)
 */
function populateGoalUpdateCommand(
    ref: SmartStepInit,
    state: SmartStepState,
    cmd: GoalUpdateCommand
) {
    const statusControl = true;
    const goalPlanControl = true;
    const showTargetScheduleControl = false;
    const recordScheduleControl = false;
    const statusChangeReasonControl = false;

    const init = ref.initState;

    /*this.instanceState.populateGoalUpdateCommand(cmd);*/
    cmd.changeTargetDate(init.targetDate, state.targetDate);

    // if the user clicks 'star' and 'AchievedAndStillRelevant' we should prioritise the star
    const prioritiseStatus =
        statusControl && asNumberChange(init.status, state.status) != undefined;
    if (!prioritiseStatus && showTargetScheduleControl && recordScheduleControl) {
        // don't record '5' to 'null' - since checks aren't meant to send anything if not recorded
        /*
        if (recordScheduleControl.getStatus()) {
            cmd.changeStatus(this.initialData.status, this.recordScheduleControl.getStatus());
            if (this.recordScheduleControl.getStatus() == SmartStepStatus.AchievedAndStillRelevant) {
                cmd.setForceStatusChange();
            }
        }
        cmd.changeStatusChangeReason(this.initialData.statusChangeReasonId, this.recordScheduleControl.getStatusChangeReason());
        */
    } else {
        if (statusControl) {
            cmd.changeStatus(init.status, state.status);
        }
        if (statusChangeReasonControl) {
            /*const value = this.statusChangeReasonControl.getSelectedId();
            cmd.changeStatusChangeReason(this.initialData.statusChangeReasonId, value);*/
        }
    }

    if (state.related) {
        cmd.setRelevant();
    }

    cmd.changeExpiryDate(init.expiryDate, state.expiryDate);

    cmd.changeGoalName(init.goalName, state.goalName);

    cmd.changeScore(init.score, state.score);

    cmd.changeHierarchy(init.hierarchy, state.hierarchy);

    cmd.changePosition(init.position, state.position);

    // LAST control to check needs to be this
    if (goalPlanControl) {
        // TEMPORARY HACK PART 1/2
        // (needed until multiple smart steps are in place)
        // Works around the misuse of an unknown legacy 'feature' where changing
        // the comment box only did not trigger a 'relevant' smart step - just a
        // comment in the history against the smart step.
        // To isolate this logic as much as possible, we restrict the hack to the
        // client side where it is used. We can fix this by setting the status
        // to 'not relevant' IF no other changes have been made.
        let hasOtherChanges = cmd.hasChanges();

        // ORIGINAL BEHAVIOUR
        cmd.changeGoalPlan(init.goalPlan, state.goalPlan);

        // TEMPORARY HACK PART 2/2
        // if we have a comment change but not any other changes
        // then set the status as not relevant - if we aren't anything already
        let hasStatus = state.status;
        // ONLY if the feature is set to ignore
        let ignoreStatus = ref.initData.sessionData.isEnabled(
            "support.evidence.goalPlanIgnoresStatus"
        );
        if (ignoreStatus && !hasOtherChanges && cmd.hasChanges() && !hasStatus) {
            // NoLongerWanted is the only status we can be if we don't already
            // have a status.
            // changeStatus is the bit which actually does the HACK
            // ignore the CommentOnly - like shouldHideWhenGoalPlanWithNotRelevant, this should be a thing of the past
            cmd.changeStatus(null, SmartStepStatus.NoLongerWanted);
        }
    }
}


/**
 * Create the errors
 */
export const emitSmartStepErrors = (init: SmartStepInit, state: SmartStepState): string[] => {
    const errors: string[] = [];
    if (!state.actionDefId) {
        errors.push("action is required");
    }
    return errors;
};


/*********
 * WIRING
 */

export const SmartStepWrapper: FC<{
    state: SmartStepState;
    showMenu: boolean;
    allowQuestionAnswerMenuItem: boolean;
}> = props => {
    const {init, state: pageState, dispatch} = useEvidencePageContext();

    const stateSetter = useCallback((s: Partial<SmartStepState>) => {
        dispatch({
            type: "updateSmartStep",
            data: s,
            controlUuid: props.state.controlUuid
        });
    }, []);
    const remove = useCallback(() => {
        dispatch({
            type: "removeSmartStep",
            controlUuid: props.state.controlUuid
        });
    }, []);

    const smartStepInit = smartStepInitLookup(
        init.initSmartSteps,
        pageState.smartStepsProvisioned,
        props.state.controlUuid
    );

    const Step = useMemo(
        () => (
            <>
                <SmartStep
                    key={smartStepInit.initState.controlUuid.toString()}
                    init={smartStepInit}
                    state={props.state}
                    stateSetter={stateSetter}
                    remove={remove}
                />
                {props.showMenu && (
                    <SmartStepMenu
                        actionDefId={props.state.actionDefId!}
                        allowQuestionAnswerMenuItem={props.allowQuestionAnswerMenuItem}
                    />
                )}
            </>
        ),
        [props.state, pageState.pageEditing]
    );

    return (
        <>
            {Step}
        </>
    );
};

/**
 * Register the commandForm, if using SmartStepCommandForm
 * NB a command-based version isn't used because the args are closures, so it needs a context OR a 'stateHolder = useMemo<{state: State}>'.
 */
/*
const useSmartStepCommandSourceRegistration = (init: SmartStepInit, state: SmartStepState) => {
    const cmdSrc: CommandSource = {
        emitChangesTo: function (cmdQ: CommandQueue): void {
            emitSmartStepCommand(init, state, cmdQ);
        },
        getErrors(): string[] {
            return emitSmartStepErrors(init, state);
        }
    };
    useCommandSourceRegistration(cmdSrc);
};

/!**
 * Register the commandForm, if required
 *!/
export const SmartStepCommandForm: FC<{init: SmartStepInit; state: SmartStepState}> = props => {
    useSmartStepCommandSourceRegistration(props.init, props.state);
    return <>{props.children}</>;
};
*/
