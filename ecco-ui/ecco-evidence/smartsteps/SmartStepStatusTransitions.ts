import {EvidencePageType, SmartStepDisplaySymbol, SmartStepStatus} from "ecco-dto";

/**
 * The representation of a smart step status (+/*) can be displayed differently according to the screen on show.
 *
 * For a typical 'needs assessment' screen, we show:
 *  faded '+': not yet WantToAchieve
 *  full '+' : WantToAchieve or Achieved (because the support workers interpretation is that a full '+' means its
 * added to the clients support plan - the actual status doesn't matter).
 *
 * For a typical 'support plan' screen, we show:
 *  -no show-: not yet WantToAchieve (support plans only show what the clients want to achieve)
 *  faded '*': WantToAchieve
 *  full '*' : Achieved
 *
 * For a typical 'review' screen, we show:
 *  faded '+': not yet WantToAchieve
 *  full '+': WantToAchieve (if new)
 *  faded '*': WantToAchieve (if pre-existing)
 *  full '*' : Achieved
 *
 * The way to determine which logic to show is from the service type settings:
 *  'asAssessment': means use the page like a 'needs assessment'
 *  'asReduction': means use the page like a 'support plan' ('reducing' the needs of the client avoided conflicting language)
 *  review: uses both asAssessment & asReduction
 */
export class SmartStepStatusTransitions {
    constructor(
        private evidencePageType: EvidencePageType | null,
        private allowActionStraightToAchieve: boolean,
        private allowUndo = false
    ) {}

    public setAllowUndo(allowUndo: boolean) {
        this.allowUndo = allowUndo;
    }

    public getAllowedStatuses(initialStatus: SmartStepStatus | null): SmartStepStatus[] {
        const statuses: SmartStepStatus[] = [];
        let status = initialStatus;
        do {
            status && statuses.push(status);
            status = this.getNextStatus(initialStatus, status);
        } while (status && !statuses.find(s => s == status));
        return statuses;
    }

    /**
     * Determines the next state of the smart step when its been clicked on
     * @param initialStatus the status of the smart step from the database
     * @param currentStatus the status of the smart step on the current page
     */
    public getNextStatus(
        initialStatus: SmartStepStatus | null,
        currentStatus: SmartStepStatus | null
    ): SmartStepStatus | null {
        //if (this.evidencePageType == EvidencePageType.supportPlan) {
        // for a commentOnly, we ignore the page type and just swap between comment and WantToAchieve
        if (initialStatus == SmartStepStatus.CommentOnly) {
            // if we are currently a comment, we can be a goal
            if (currentStatus == SmartStepStatus.CommentOnly) {
                return SmartStepStatus.WantToAchieve;
            }
            // if we are currently a goal, we can be a comment
            return SmartStepStatus.CommentOnly;
        }
        /*
        // allow commentOnly to WantToAchieve
        if (this.evidencePageType == EvidencePageType.needsSupportPlan) {
            if (currentStatus == null || currentStatus == SmartStepStatus.CommentOnly) {
                // if we are currently a comment, we can be a goal
                return SmartStepStatus.WantToAchieve;
            }
        }
        */

        // asAssessment - ignores click on achieved
        if (this.evidencePageType == EvidencePageType.assessment) {
            if (currentStatus == null || currentStatus == SmartStepStatus.NoLongerWanted) {
                return SmartStepStatus.WantToAchieve;
            }
            // only allow an 'undo' if the symbol started faded
            if (
                currentStatus == SmartStepStatus.WantToAchieve &&
                (initialStatus == null || initialStatus == SmartStepStatus.NoLongerWanted)
            ) {
                return initialStatus;
            }
            // allowUndo works on pre-saved states (current == initial)
            if (this.allowUndo && currentStatus == initialStatus) {
                if (
                    currentStatus == SmartStepStatus.WantToAchieve ||
                    currentStatus == SmartStepStatus.Achieved ||
                    currentStatus == SmartStepStatus.Unachieved
                ) {
                    return SmartStepStatus.NoLongerWanted;
                }
            }
        }

        // asReduction
        if (this.evidencePageType == EvidencePageType.reduction) {
            // only allow us to move forwards if we started as WantToAchieve - because we could be a new smart step
            // so we mimic the behaviour of the review of not allowing the status to go to Achieved for newly WantToAchieve
            if (
                (currentStatus == SmartStepStatus.WantToAchieve &&
                    initialStatus == SmartStepStatus.WantToAchieve) ||
                (currentStatus == SmartStepStatus.Unachieved &&
                    initialStatus == SmartStepStatus.Unachieved)
            ) {
                return SmartStepStatus.Achieved;
            }
            if (!this.allowUndo && currentStatus == SmartStepStatus.Achieved) {
                return initialStatus;
            }
            // allowUndo works on pre-saved states (current == initial)
            if (
                this.allowUndo &&
                currentStatus == initialStatus &&
                currentStatus == SmartStepStatus.Achieved
            ) {
                return SmartStepStatus.WantToAchieve;
            }
        }

        // asAssessmentAndReduction
        // with allowActionStraightToAchieve
        if (
            this.allowActionStraightToAchieve &&
            (this.evidencePageType == EvidencePageType.review ||
                this.evidencePageType == EvidencePageType.assessmentReduction)
        ) {
            // allowUndo works on pre-saved states (current == initial)
            if (this.allowUndo && currentStatus == initialStatus) {
                if (currentStatus == SmartStepStatus.Achieved) {
                    return SmartStepStatus.WantToAchieve;
                } else {
                    return SmartStepStatus.NoLongerWanted;
                }
            }

            if (currentStatus == null || currentStatus == SmartStepStatus.NoLongerWanted) {
                return SmartStepStatus.WantToAchieve;
            }
            if (
                currentStatus == SmartStepStatus.WantToAchieve ||
                currentStatus == SmartStepStatus.Unachieved
            ) {
                return SmartStepStatus.Achieved;
            }
            if (currentStatus == SmartStepStatus.Achieved) {
                return initialStatus; // After Achieved we go to whatever the database initially had
            }
        }

        // asAssessmentAndReduction
        // without allowActionStraightToAchieve
        if (
            !this.allowActionStraightToAchieve &&
            (this.evidencePageType == EvidencePageType.review ||
                this.evidencePageType == EvidencePageType.assessmentReduction)
        ) {
            // allowUndo works on pre-saved states (current == initial)
            if (this.allowUndo && currentStatus == initialStatus) {
                if (currentStatus == SmartStepStatus.Achieved) {
                    return SmartStepStatus.WantToAchieve;
                } else {
                    return SmartStepStatus.NoLongerWanted;
                }
            }

            // toggle back and forth between not wanted and wanted
            if (currentStatus == null || currentStatus == SmartStepStatus.NoLongerWanted) {
                return SmartStepStatus.WantToAchieve;
            }
            if (
                currentStatus == SmartStepStatus.WantToAchieve &&
                (initialStatus == null || initialStatus == SmartStepStatus.NoLongerWanted)
            ) {
                return initialStatus;
            }

            // toggle back and forth between wanted and achieved
            if (
                currentStatus == SmartStepStatus.WantToAchieve ||
                currentStatus == SmartStepStatus.Unachieved
            ) {
                return SmartStepStatus.Achieved;
            }
            if (currentStatus == SmartStepStatus.Achieved) {
                return initialStatus;
            }
        }

        // return the same status so there is no change
        return currentStatus;
    }

    /**
     * Determines the symbol of the current state.
     * Uses the initialised state of the smart step when combining needs
     * and support (assessment and reduction) on the same screen
     * since assessment clicks go to full plus, not a faded star.
     * We want to change the display and keep the status consistent.
     * @param initialStatus the status of the smart step from the database
     * @param currentStatus the status of the smart step on the current page
     */
    public getDisplaySymbol(
        initialStatus: SmartStepStatus,
        status: SmartStepStatus
    ): SmartStepDisplaySymbol {
        // asAssessment
        if (this.evidencePageType == EvidencePageType.assessment) {
            switch (status) {
                case SmartStepStatus.WantToAchieve: // fallthru
                case SmartStepStatus.Unachieved:
                    return SmartStepDisplaySymbol.FullPlus;
                case SmartStepStatus.Achieved:
                    return SmartStepDisplaySymbol.FullPlus;
                case SmartStepStatus.CommentOnly:
                    return SmartStepDisplaySymbol.CommentOnly;
                default:
                    // no status, no data, means 'notWantToAchieve' if we had such a status
                    return SmartStepDisplaySymbol.FadedPlus;
            }
        }

        // asReduction
        if (this.evidencePageType == EvidencePageType.reduction) {
            switch (status) {
                case SmartStepStatus.WantToAchieve:
                case SmartStepStatus.Unachieved: // fallthru
                    return SmartStepDisplaySymbol.FadedStar;
                case SmartStepStatus.Achieved:
                    return SmartStepDisplaySymbol.FullStar;
                case SmartStepStatus.CommentOnly:
                    return SmartStepDisplaySymbol.CommentOnly;
                default:
                    // no status, no data, means 'notWantToAchieve' if we had such a status
                    return SmartStepDisplaySymbol.FadedPlus;
            }
        }

        // asAssessmentAndReduction
        if (
            this.evidencePageType == EvidencePageType.review ||
            this.evidencePageType == EvidencePageType.assessmentReduction
        ) {
            switch (status) {
                case SmartStepStatus.WantToAchieve:
                    if (initialStatus == null || initialStatus == SmartStepStatus.NoLongerWanted) {
                        return SmartStepDisplaySymbol.FullPlus;
                    }
                    return SmartStepDisplaySymbol.FadedStar;
                case SmartStepStatus.Unachieved: // fallthru
                    return SmartStepDisplaySymbol.FadedStar;
                case SmartStepStatus.Achieved:
                    return SmartStepDisplaySymbol.FullStar;
                case SmartStepStatus.CommentOnly:
                    return SmartStepDisplaySymbol.CommentOnly;
                default:
                    // no status, no data, means 'notWantToAchieve' if we had such a status
                    return SmartStepDisplaySymbol.FadedPlus;
            }
        }

        throw new Error("undetermined SmartStepDisplaySymbol");
    }
}
